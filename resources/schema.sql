-- MySQL dump 10.13  Distrib 8.0.42, for Linux (x86_64)
--
-- Host: 127.0.0.1    Database: ngora
-- ------------------------------------------------------
-- Server version	8.0.42-0ubuntu0.24.04.2

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `activity`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `activity` (
  `activity_id` int NOT NULL AUTO_INCREMENT,
  `visible_to_all` varchar(5) DEFAULT NULL,
  `title` varchar(100) DEFAULT NULL,
  `category` varchar(50) DEFAULT NULL,
  `details` varchar(1000) DEFAULT NULL,
  `venue` varchar(100) DEFAULT NULL,
  `facilitator` varchar(100) DEFAULT NULL,
  `district_id` varchar(5) DEFAULT NULL,
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `record_control` varchar(5) DEFAULT NULL,
  `captured_by` varchar(50) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `complaint_id` (`activity_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_category`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `activity_category` (
  `activity_category_id` int NOT NULL AUTO_INCREMENT,
  `category` varchar(100) DEFAULT NULL,
  `description` varchar(100) DEFAULT NULL,
  `captured_by` varchar(50) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`activity_category_id`)
) ENGINE=MyISAM AUTO_INCREMENT=3 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_participant`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `activity_participant` (
  `activity_participant_id` int NOT NULL AUTO_INCREMENT,
  `activity_id` varchar(20) DEFAULT NULL,
  `username` varchar(50) DEFAULT NULL,
  `firstname` varchar(100) DEFAULT NULL,
  `lastname` varchar(100) DEFAULT NULL,
  `telephone` varchar(100) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `is_invited` varchar(5) DEFAULT NULL,
  `attended` varchar(5) DEFAULT NULL,
  `is_rsvp` varchar(5) DEFAULT NULL,
  `rsvp_by` varchar(50) DEFAULT NULL,
  `rsvp_date` datetime DEFAULT NULL,
  `captured_by` varchar(50) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `complaint_id` (`activity_participant_id`)
) ENGINE=InnoDB AUTO_INCREMENT=339 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `area_development_committee`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `area_development_committee` (
  `area_development_committee_id` int NOT NULL AUTO_INCREMENT,
  `district_id` varchar(5) DEFAULT NULL,
  `area_development_committee` varchar(100) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `sector_id` (`area_development_committee_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `audit_trail`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `audit_trail` (
  `audit_trail_id` bigint NOT NULL AUTO_INCREMENT,
  `action` varchar(20) DEFAULT NULL,
  `detail` varchar(1000) DEFAULT NULL,
  `action_by` varchar(50) NOT NULL,
  `action_date` datetime DEFAULT NULL,
  `table_name` varchar(20) DEFAULT NULL,
  `record_id` varchar(1000) DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `audit_trail_id` (`audit_trail_id`)
) ENGINE=InnoDB AUTO_INCREMENT=556478 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `bank`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `bank` (
  `bank_id` int NOT NULL AUTO_INCREMENT,
  `bank_code` varchar(5) DEFAULT NULL,
  `bank_name` varchar(100) DEFAULT NULL,
  `captured_by` varchar(50) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`bank_id`)
) ENGINE=MyISAM AUTO_INCREMENT=9 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `certificate`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `certificate` (
  `certificate_id` int NOT NULL AUTO_INCREMENT,
  `organization_id` varchar(20) DEFAULT NULL,
  `certificate_category` varchar(100) DEFAULT NULL,
  `details_1` varchar(100) DEFAULT NULL,
  `details_2` varchar(100) DEFAULT NULL,
  `start_date` varchar(10) DEFAULT NULL,
  `end_date` varchar(10) DEFAULT NULL,
  `is_printed` varchar(5) DEFAULT NULL,
  `last_printed_by` varchar(20) DEFAULT NULL,
  `last_printed_date` datetime DEFAULT NULL,
  `invoice_number` varchar(20) DEFAULT NULL,
  `record_control` varchar(5) NOT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `certificate_id` (`certificate_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2854 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `complaint`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `complaint` (
  `complaint_id` int NOT NULL AUTO_INCREMENT,
  `lodge_anonymous` varchar(5) DEFAULT NULL,
  `organization_id` varchar(100) DEFAULT NULL,
  `summary` varchar(200) DEFAULT NULL,
  `complaint_category_id` varchar(20) DEFAULT NULL,
  `complaint_details` varchar(1000) DEFAULT NULL,
  `attachment` varchar(100) DEFAULT NULL,
  `attachment_display` varchar(100) DEFAULT NULL,
  `record_control` varchar(5) DEFAULT NULL,
  `complainant_organization_id` varchar(20) DEFAULT NULL,
  `resolution` varchar(1000) DEFAULT NULL,
  `captured_by` varchar(50) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `complaint_id` (`complaint_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `complaint_category`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `complaint_category` (
  `complaint_category_id` int NOT NULL AUTO_INCREMENT,
  `category` varchar(100) DEFAULT NULL,
  `description` varchar(100) DEFAULT NULL,
  `captured_by` varchar(50) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`complaint_category_id`)
) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `complaint_handling`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `complaint_handling` (
  `complaint_handling_id` int NOT NULL AUTO_INCREMENT,
  `complaint_id` varchar(20) DEFAULT NULL,
  `action` varchar(20) DEFAULT NULL,
  `notes` varchar(1000) DEFAULT NULL,
  `attachment_1` varchar(100) DEFAULT NULL,
  `attachment_2` varchar(100) DEFAULT NULL,
  `attachment_display_1` varchar(100) DEFAULT NULL,
  `attachment_display_2` varchar(100) DEFAULT NULL,
  `captured_by` varchar(50) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `complaint_handling_id` (`complaint_handling_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `country`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `country` (
  `country_id` int NOT NULL AUTO_INCREMENT,
  `country_code` varchar(5) DEFAULT NULL,
  `country_name` varchar(50) DEFAULT NULL,
  `captured_by` varchar(50) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`country_id`)
) ENGINE=MyISAM AUTO_INCREMENT=245 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cron`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cron` (
  `cron_id` int NOT NULL AUTO_INCREMENT,
  `record_control` varchar(5) DEFAULT NULL,
  `test` varchar(1000) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `certificate_period_id` (`cron_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `currency`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `currency` (
  `currency_id` int NOT NULL AUTO_INCREMENT,
  `currency` varchar(5) DEFAULT NULL,
  `description` varchar(50) DEFAULT NULL,
  `exchange_rate` float DEFAULT NULL,
  `is_default` varchar(5) DEFAULT NULL,
  `captured_by` varchar(50) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`currency_id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `district`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `district` (
  `district_id` int NOT NULL AUTO_INCREMENT,
  `district_code` varchar(5) DEFAULT NULL,
  `district_name` varchar(50) NOT NULL,
  `zone_id` varchar(20) DEFAULT NULL,
  `region_id` varchar(20) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `district_id` (`district_id`)
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `donor`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `donor` (
  `donor_id` int NOT NULL AUTO_INCREMENT,
  `donor` varchar(100) DEFAULT NULL,
  `captured_by` varchar(50) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`donor_id`)
) ENGINE=MyISAM AUTO_INCREMENT=8 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `enabler`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `enabler` (
  `enabler_id` int NOT NULL AUTO_INCREMENT,
  `pillar` varchar(100) DEFAULT NULL,
  `enabler` varchar(100) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `sector_id` (`enabler_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `fee`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fee` (
  `fee_id` int NOT NULL AUTO_INCREMENT,
  `fee_category` varchar(100) DEFAULT NULL,
  `invoice_time` varchar(20) DEFAULT NULL,
  `currency` varchar(20) DEFAULT NULL,
  `based_on_income` varchar(5) DEFAULT NULL,
  `from_income` varchar(20) DEFAULT NULL,
  `to_income` varchar(20) DEFAULT NULL,
  `amount` varchar(20) DEFAULT NULL,
  `captured_by` varchar(50) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`fee_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `invoice`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `invoice` (
  `invoice_id` int NOT NULL AUTO_INCREMENT,
  `invoice_number` varchar(50) DEFAULT NULL,
  `organization_id` varchar(20) DEFAULT NULL,
  `fee_category` varchar(100) DEFAULT NULL,
  `details` varchar(100) DEFAULT NULL,
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `invoice_year` varchar(5) DEFAULT NULL,
  `amount` varchar(20) DEFAULT NULL,
  `due_date` datetime DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`invoice_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1785 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `licensing_organization`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `licensing_organization` (
  `licensing_organization_id` int NOT NULL AUTO_INCREMENT,
  `organization_id` varchar(20) DEFAULT NULL,
  `abbreviation` varchar(10) DEFAULT NULL,
  `organization_name` varchar(100) DEFAULT NULL,
  `charity_number` varchar(20) DEFAULT NULL,
  `telephone` varchar(50) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `registration_type` varchar(50) DEFAULT NULL,
  `organization_type` varchar(10) DEFAULT NULL,
  `postal_address` varchar(200) DEFAULT NULL,
  `physical_address` varchar(200) DEFAULT NULL,
  `district_id` varchar(20) DEFAULT NULL,
  `executive_director_fullname` varchar(100) DEFAULT NULL,
  `executive_director_gender` varchar(10) DEFAULT NULL,
  `executive_director_nationality` varchar(50) DEFAULT NULL,
  `executive_director_national_id` varchar(20) DEFAULT NULL,
  `executive_director_highest_qualification` varchar(20) DEFAULT NULL,
  `executive_director_email` varchar(100) DEFAULT NULL,
  `executive_director_telephone` varchar(50) DEFAULT NULL,
  `financial_year_start_month` varchar(20) DEFAULT NULL,
  `financial_year_end_month` varchar(20) DEFAULT NULL,
  `annual_income` varchar(20) DEFAULT NULL,
  `record_control` varchar(5) DEFAULT NULL,
  `reporting_year` varchar(5) DEFAULT NULL,
  `payment_processed_by` varchar(20) DEFAULT NULL,
  `payment_processed_date` datetime DEFAULT NULL,
  `approved1_by` varchar(20) DEFAULT NULL,
  `approved1_date` datetime DEFAULT NULL,
  `approved1_comments` varchar(200) DEFAULT NULL,
  `approved2_by` varchar(20) DEFAULT NULL,
  `approved2_date` datetime DEFAULT NULL,
  `approved2_comments` varchar(200) DEFAULT NULL,
  `rejected_by` varchar(20) DEFAULT NULL,
  `rejected_date` datetime DEFAULT NULL,
  `rejected_comments` varchar(200) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `organization_id` (`licensing_organization_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3255 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `licensing_organization_auditor`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `licensing_organization_auditor` (
  `auditor_id` int NOT NULL AUTO_INCREMENT,
  `licensing_organization_id` varchar(20) NOT NULL,
  `name` varchar(100) DEFAULT NULL,
  `address` varchar(200) DEFAULT NULL,
  `telephone` varchar(50) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `auditor_id` (`auditor_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2658 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `licensing_organization_bank`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `licensing_organization_bank` (
  `organization_bank_id` int NOT NULL AUTO_INCREMENT,
  `licensing_organization_id` varchar(20) NOT NULL,
  `bank_id` varchar(20) DEFAULT NULL,
  `address` varchar(200) DEFAULT NULL,
  `telephone` varchar(50) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `details_1` varchar(100) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `organization_bank_id` (`organization_bank_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2917 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `licensing_organization_document`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `licensing_organization_document` (
  `document_id` int NOT NULL AUTO_INCREMENT,
  `licensing_organization_id` varchar(20) NOT NULL,
  `document_category` varchar(50) DEFAULT NULL,
  `filename` varchar(100) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `document_id` (`document_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6767 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `licensing_organization_location_activity`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `licensing_organization_location_activity` (
  `location_activity_id` int NOT NULL AUTO_INCREMENT,
  `licensing_organization_id` varchar(20) NOT NULL,
  `project_id` varchar(20) DEFAULT NULL,
  `vdc` varchar(100) DEFAULT NULL,
  `adc` varchar(100) DEFAULT NULL,
  `adc_other` varchar(100) DEFAULT NULL,
  `district_id` varchar(20) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `location_activity_id` (`location_activity_id`)
) ENGINE=InnoDB AUTO_INCREMENT=30379 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `licensing_organization_objective`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `licensing_organization_objective` (
  `objective_id` int NOT NULL AUTO_INCREMENT,
  `licensing_organization_id` varchar(20) NOT NULL,
  `objective` varchar(200) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `objective_id` (`objective_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9495 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `licensing_organization_project`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `licensing_organization_project` (
  `project_id` int NOT NULL AUTO_INCREMENT,
  `licensing_organization_id` varchar(20) NOT NULL,
  `project_name` varchar(100) DEFAULT NULL,
  `thematic_area` varchar(100) DEFAULT NULL,
  `thematic_area_other` varchar(100) DEFAULT NULL,
  `pillar` varchar(100) DEFAULT NULL,
  `enabler` varchar(100) DEFAULT NULL,
  `number_of_beneficiaries` varchar(20) DEFAULT NULL,
  `amount_spent` varchar(20) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `sector_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `licensing_organization_project_intervention`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `licensing_organization_project_intervention` (
  `project_intervention_id` int NOT NULL AUTO_INCREMENT,
  `licensing_organization_id` varchar(20) DEFAULT NULL,
  `project_id` varchar(20) DEFAULT NULL,
  `project_intervention` varchar(200) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `sector_id` (`project_intervention_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `licensing_organization_project_output`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `licensing_organization_project_output` (
  `project_output_id` int NOT NULL AUTO_INCREMENT,
  `licensing_organization_id` varchar(20) DEFAULT NULL,
  `project_id` varchar(20) DEFAULT NULL,
  `project_output` varchar(200) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `sector_id` (`project_output_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `licensing_organization_sector`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `licensing_organization_sector` (
  `sector_id` int NOT NULL AUTO_INCREMENT,
  `licensing_organization_id` varchar(20) NOT NULL,
  `project_id` varchar(20) DEFAULT NULL,
  `sector` varchar(50) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `sector_id` (`sector_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8614 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `licensing_organization_source_funding`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `licensing_organization_source_funding` (
  `source_funding_id` int NOT NULL AUTO_INCREMENT,
  `licensing_organization_id` varchar(20) NOT NULL,
  `donor_id` varchar(20) DEFAULT NULL,
  `contact_details` varchar(200) DEFAULT NULL,
  `funding_currency` varchar(5) DEFAULT NULL,
  `funding_amount` varchar(20) DEFAULT NULL,
  `funding_amount_local` varchar(20) DEFAULT NULL,
  `details_1` varchar(100) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `source_funding_id` (`source_funding_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6091 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `licensing_organization_staff_capacity`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `licensing_organization_staff_capacity` (
  `staff_capacity_id` int NOT NULL AUTO_INCREMENT,
  `licensing_organization_id` varchar(20) NOT NULL,
  `staff_type` varchar(50) DEFAULT NULL,
  `staff_gender` varchar(10) DEFAULT NULL,
  `staff_number` varchar(20) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `staff_capacity_id` (`staff_capacity_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6224 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `licensing_organization_target_group`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `licensing_organization_target_group` (
  `target_group_id` int NOT NULL AUTO_INCREMENT,
  `licensing_organization_id` varchar(20) NOT NULL,
  `project_id` varchar(20) DEFAULT NULL,
  `target_group` varchar(50) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `target_group_id` (`target_group_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3547 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `licensing_organization_tep`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `licensing_organization_tep` (
  `tep_id` int NOT NULL AUTO_INCREMENT,
  `licensing_organization_id` varchar(20) DEFAULT NULL,
  `fullname` varchar(100) DEFAULT NULL,
  `nationality` varchar(50) DEFAULT NULL,
  `passport_number` varchar(20) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `tep_id` (`tep_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=115 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `licensing_organization_trustee`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `licensing_organization_trustee` (
  `trustee_id` int NOT NULL AUTO_INCREMENT,
  `licensing_organization_id` varchar(20) NOT NULL,
  `fullname` varchar(100) DEFAULT NULL,
  `gender` varchar(10) DEFAULT NULL,
  `telephone` varchar(50) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `occupation` varchar(50) DEFAULT NULL,
  `nationality` varchar(50) DEFAULT NULL,
  `national_id` varchar(20) DEFAULT NULL,
  `position` varchar(50) DEFAULT NULL,
  `timeframe` varchar(50) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `trustee_id` (`trustee_id`)
) ENGINE=InnoDB AUTO_INCREMENT=13441 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `menu`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `menu` (
  `menu_id` bigint NOT NULL AUTO_INCREMENT,
  `menu_item` varchar(100) NOT NULL,
  `menu_link` varchar(100) DEFAULT NULL,
  `menu_parent` varchar(100) DEFAULT NULL,
  `weight` int NOT NULL,
  `icon` varchar(50) DEFAULT NULL,
  `icon_color` varchar(10) DEFAULT NULL,
  `captured_by` varchar(50) NOT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) NOT NULL,
  UNIQUE KEY `menu_id` (`menu_id`)
) ENGINE=InnoDB AUTO_INCREMENT=98 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `module_public`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `module_public` (
  `module_public_id` int NOT NULL AUTO_INCREMENT,
  `module_ids` varchar(1000) DEFAULT NULL,
  `captured_by` varchar(50) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`module_public_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ngo_card_sharing`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ngo_card_sharing` (
  `ngo_card_sharing_id` int NOT NULL AUTO_INCREMENT,
  `ngo_card_sharing` varchar(1000) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`ngo_card_sharing_id`)
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `notice`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notice` (
  `notice_id` int NOT NULL AUTO_INCREMENT,
  `notice_title` varchar(100) DEFAULT NULL,
  `notice_content` text,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`notice_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `organization`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `organization` (
  `organization_id` int NOT NULL AUTO_INCREMENT,
  `abbreviation` varchar(10) DEFAULT NULL,
  `organization_name` varchar(100) DEFAULT NULL,
  `charity_number` varchar(20) DEFAULT NULL,
  `telephone` varchar(50) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `registration_type` varchar(50) DEFAULT NULL,
  `organization_type` varchar(10) DEFAULT NULL COMMENT 'Organization Type',
  `postal_address` varchar(200) DEFAULT NULL,
  `physical_address` varchar(200) DEFAULT NULL,
  `district_id` varchar(20) DEFAULT NULL,
  `executive_director_fullname` varchar(100) DEFAULT NULL,
  `executive_director_gender` varchar(10) DEFAULT NULL,
  `executive_director_nationality` varchar(50) DEFAULT NULL,
  `executive_director_national_id` varchar(20) DEFAULT NULL,
  `executive_director_highest_qualification` varchar(20) DEFAULT NULL,
  `executive_director_email` varchar(100) DEFAULT NULL,
  `executive_director_telephone` varchar(50) DEFAULT NULL,
  `financial_year_start_month` varchar(20) DEFAULT NULL,
  `financial_year_end_month` varchar(20) DEFAULT NULL,
  `annual_income` varchar(20) DEFAULT NULL,
  `registration_number` varchar(20) DEFAULT NULL,
  `registration_year` varchar(5) DEFAULT NULL COMMENT 'Registration Year',
  `record_control` varchar(5) DEFAULT NULL,
  `payment_processed_by` varchar(20) DEFAULT NULL,
  `payment_processed_date` datetime DEFAULT NULL,
  `approved1_by` varchar(20) DEFAULT NULL,
  `approved1_date` datetime DEFAULT NULL,
  `approved1_comments` varchar(200) DEFAULT NULL,
  `approved2_by` varchar(20) DEFAULT NULL,
  `approved2_date` datetime DEFAULT NULL,
  `approved2_comments` varchar(200) DEFAULT NULL,
  `rejected_by` varchar(20) DEFAULT NULL,
  `rejected_date` datetime DEFAULT NULL,
  `rejected_comments` varchar(200) DEFAULT NULL,
  `disabled_by` varchar(20) DEFAULT NULL,
  `disabled_date` datetime DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `organization_id` (`organization_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2645 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`brian`@`localhost`*/ /*!50003 TRIGGER `trigger_log_activity_on_disable` AFTER UPDATE ON `organization` FOR EACH ROW BEGIN
	IF (NEW.record_control = '5') THEN
 		INSERT INTO audit_trail 
        SET action = 'DISABLE', 
        detail = CONCAT('Inactive organization ', NEW.organization_name, IF(LENGTH(NEW.abbreviation) > 0, CONCAT(' (', NEW.abbreviation, ')'), ''), ' - ', NEW.registration_number, ' successfully disabled'), 
        action_by = NEW.disabled_by, 
        action_date = NOW(), 
        table_name = 'Organization', 
        record_id = NEW.organization_id, 
        status = 'active';
	END IF;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;

--
-- Table structure for table `organization_auditor`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `organization_auditor` (
  `auditor_id` int NOT NULL AUTO_INCREMENT,
  `organization_id` varchar(20) NOT NULL,
  `name` varchar(100) DEFAULT NULL,
  `address` varchar(200) DEFAULT NULL,
  `telephone` varchar(50) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `auditor_id` (`auditor_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2440 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `organization_bank`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `organization_bank` (
  `organization_bank_id` int NOT NULL AUTO_INCREMENT,
  `organization_id` varchar(20) NOT NULL,
  `bank_id` varchar(20) DEFAULT NULL,
  `address` varchar(200) DEFAULT NULL,
  `telephone` varchar(50) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `details_1` varchar(100) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `organization_bank_id` (`organization_bank_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2719 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `organization_document`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `organization_document` (
  `document_id` int NOT NULL AUTO_INCREMENT,
  `organization_id` varchar(20) NOT NULL,
  `document_category` varchar(50) DEFAULT NULL,
  `filename` varchar(100) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `document_id` (`document_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4522 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `organization_location_activity`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `organization_location_activity` (
  `location_activity_id` int NOT NULL AUTO_INCREMENT,
  `organization_id` varchar(20) NOT NULL,
  `project_id` varchar(20) DEFAULT NULL,
  `vdc` varchar(100) DEFAULT NULL,
  `adc` varchar(100) DEFAULT NULL,
  `adc_other` varchar(100) DEFAULT NULL,
  `district_id` varchar(20) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `location_activity_id` (`location_activity_id`)
) ENGINE=InnoDB AUTO_INCREMENT=24042 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `organization_objective`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `organization_objective` (
  `objective_id` int NOT NULL AUTO_INCREMENT,
  `organization_id` varchar(20) NOT NULL,
  `objective` varchar(200) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `objective_id` (`objective_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8919 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `organization_project`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `organization_project` (
  `project_id` int NOT NULL AUTO_INCREMENT,
  `organization_id` varchar(20) NOT NULL,
  `project_name` varchar(100) DEFAULT NULL,
  `thematic_area` varchar(100) DEFAULT NULL,
  `thematic_area_other` varchar(100) DEFAULT NULL,
  `pillar` varchar(100) DEFAULT NULL,
  `enabler` varchar(100) DEFAULT NULL,
  `number_of_beneficiaries` varchar(20) DEFAULT NULL,
  `amount_spent` varchar(20) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `sector_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `organization_project_intervention`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `organization_project_intervention` (
  `project_intervention_id` int NOT NULL AUTO_INCREMENT,
  `organization_id` varchar(20) DEFAULT NULL,
  `project_id` varchar(20) DEFAULT NULL,
  `project_intervention` varchar(200) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `sector_id` (`project_intervention_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `organization_project_output`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `organization_project_output` (
  `project_output_id` int NOT NULL AUTO_INCREMENT,
  `organization_id` varchar(20) DEFAULT NULL,
  `project_id` varchar(20) DEFAULT NULL,
  `project_output` varchar(200) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `sector_id` (`project_output_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `organization_sector`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `organization_sector` (
  `sector_id` int NOT NULL AUTO_INCREMENT,
  `organization_id` varchar(20) NOT NULL,
  `project_id` varchar(20) DEFAULT NULL,
  `sector` varchar(50) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `sector_id` (`sector_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7958 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `organization_source_funding`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `organization_source_funding` (
  `source_funding_id` int NOT NULL AUTO_INCREMENT,
  `organization_id` varchar(20) NOT NULL,
  `donor_id` varchar(20) DEFAULT NULL,
  `contact_details` varchar(200) DEFAULT NULL,
  `funding_currency` varchar(5) DEFAULT NULL,
  `funding_amount` varchar(20) DEFAULT NULL,
  `funding_amount_local` varchar(20) DEFAULT NULL,
  `details_1` varchar(100) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `source_funding_id` (`source_funding_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5314 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `organization_staff_capacity`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `organization_staff_capacity` (
  `staff_capacity_id` int NOT NULL AUTO_INCREMENT,
  `organization_id` varchar(20) NOT NULL,
  `staff_type` varchar(50) DEFAULT NULL,
  `staff_gender` varchar(10) DEFAULT NULL,
  `staff_number` varchar(20) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `staff_capacity_id` (`staff_capacity_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5727 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `organization_target_group`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `organization_target_group` (
  `target_group_id` int NOT NULL AUTO_INCREMENT,
  `organization_id` varchar(20) NOT NULL,
  `project_id` varchar(20) DEFAULT NULL,
  `target_group` varchar(50) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `target_group_id` (`target_group_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3340 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `organization_tep`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `organization_tep` (
  `tep_id` int NOT NULL AUTO_INCREMENT,
  `organization_id` varchar(20) DEFAULT NULL,
  `fullname` varchar(100) DEFAULT NULL,
  `nationality` varchar(50) DEFAULT NULL,
  `passport_number` varchar(20) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `tep_id` (`tep_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `organization_trustee`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `organization_trustee` (
  `trustee_id` int NOT NULL AUTO_INCREMENT,
  `organization_id` varchar(20) NOT NULL,
  `fullname` varchar(100) DEFAULT NULL,
  `gender` varchar(10) DEFAULT NULL,
  `telephone` varchar(50) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `occupation` varchar(50) DEFAULT NULL,
  `nationality` varchar(50) DEFAULT NULL,
  `national_id` varchar(20) DEFAULT NULL,
  `position` varchar(50) DEFAULT NULL,
  `timeframe` varchar(50) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `trustee_id` (`trustee_id`)
) ENGINE=InnoDB AUTO_INCREMENT=12252 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `organization_type`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `organization_type` (
  `organization_type_id` int NOT NULL AUTO_INCREMENT,
  `organization_type_code` varchar(10) DEFAULT NULL,
  `organization_type` varchar(50) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `organization_origin_id` (`organization_type_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payment`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payment` (
  `payment_id` int NOT NULL AUTO_INCREMENT,
  `invoice_number` varchar(50) DEFAULT NULL,
  `payment_mode` varchar(50) DEFAULT NULL,
  `payment_reference` varchar(200) DEFAULT NULL,
  `amount` varchar(20) DEFAULT NULL,
  `receipt_number` varchar(50) DEFAULT NULL,
  `is_reversible` varchar(5) DEFAULT NULL,
  `record_control` varchar(3) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `reversed_by` varchar(20) DEFAULT NULL,
  `reversed_date` datetime DEFAULT NULL,
  `refunded_by` varchar(20) DEFAULT NULL,
  `refunded_date` datetime DEFAULT NULL,
  `reject_comments` varchar(200) DEFAULT NULL,
  `rejected_by` varchar(20) DEFAULT NULL,
  `rejected_date` datetime DEFAULT NULL,
  `authorizer_comments` varchar(200) DEFAULT NULL,
  `authorized_date` datetime DEFAULT NULL,
  `authorized_by` varchar(20) DEFAULT NULL,
  `approval_comments` varchar(200) DEFAULT NULL,
  `approved_date` datetime DEFAULT NULL,
  `approved_by` varchar(20) DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`payment_id`),
  KEY `indx_status` (`status`),
  KEY `indx_invoice_number` (`invoice_number`),
  KEY `indx_receipt_number` (`receipt_number`),
  KEY `indx_is_reversible` (`is_reversible`),
  KEY `indx_record_control` (`record_control`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1860 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payment_mode`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payment_mode` (
  `payment_mode_id` int NOT NULL AUTO_INCREMENT,
  `payment_mode` varchar(50) DEFAULT NULL,
  `captured_by` varchar(50) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`payment_mode_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `period`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `period` (
  `period_id` int NOT NULL AUTO_INCREMENT,
  `invoice_time` varchar(20) DEFAULT NULL,
  `start_date` varchar(10) DEFAULT NULL,
  `end_date` varchar(10) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `certificate_period_id` (`period_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pillar`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pillar` (
  `pillar_id` int NOT NULL AUTO_INCREMENT,
  `pillar` varchar(100) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `sector_id` (`pillar_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `project`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `project` (
  `project_id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  `description` varchar(200) DEFAULT NULL,
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `project_status` varchar(20) DEFAULT NULL,
  `captured_by` varchar(50) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `complaint_id` (`project_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `project_field`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `project_field` (
  `project_field_id` int NOT NULL AUTO_INCREMENT,
  `project_id` varchar(20) DEFAULT NULL,
  `name` varchar(50) DEFAULT NULL,
  `data_type` varchar(20) DEFAULT NULL,
  `is_required` varchar(5) DEFAULT NULL,
  `min` varchar(5) DEFAULT NULL,
  `max` varchar(5) DEFAULT NULL,
  `number_of_decimals` varchar(5) DEFAULT NULL,
  `options` varchar(1000) DEFAULT NULL,
  `select_multiple` varchar(5) DEFAULT NULL,
  `target_table` varchar(50) DEFAULT NULL,
  `display_on_list` varchar(5) DEFAULT NULL,
  `sort_order` varchar(5) DEFAULT NULL,
  `captured_by` varchar(50) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `project_field_id` (`project_field_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `project_field_data`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `project_field_data` (
  `project_field_data_id` int NOT NULL AUTO_INCREMENT,
  `project_field_id` varchar(20) DEFAULT NULL,
  `transaction_id` varchar(20) DEFAULT NULL,
  `value` varchar(1000) DEFAULT NULL,
  `captured_by` varchar(50) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `field_data_id` (`project_field_data_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qualification`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qualification` (
  `qualification_id` int NOT NULL AUTO_INCREMENT,
  `qualification` varchar(20) DEFAULT NULL,
  `captured_by` varchar(50) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`qualification_id`)
) ENGINE=MyISAM AUTO_INCREMENT=7 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `region`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `region` (
  `region_id` int NOT NULL AUTO_INCREMENT,
  `region_name` varchar(50) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `region_id` (`region_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `registration_type`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `registration_type` (
  `registration_type_id` int NOT NULL AUTO_INCREMENT,
  `registration_type` varchar(100) DEFAULT NULL,
  `captured_by` varchar(50) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`registration_type_id`)
) ENGINE=MyISAM AUTO_INCREMENT=3 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `report`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `report` (
  `report_id` int NOT NULL AUTO_INCREMENT,
  `report_name` varchar(100) DEFAULT NULL,
  `description` varchar(200) DEFAULT NULL,
  `destination` varchar(50) DEFAULT NULL,
  `sort_order` int DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`report_id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `report_destination`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `report_destination` (
  `report_destination_id` int NOT NULL AUTO_INCREMENT,
  `destination_code` varchar(5) DEFAULT NULL,
  `destination` varchar(100) DEFAULT NULL,
  `display_section` varchar(50) DEFAULT NULL,
  `sort_order` int DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`report_destination_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `role`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role` (
  `role_id` int NOT NULL AUTO_INCREMENT,
  `role_name` varchar(100) DEFAULT NULL,
  `menu_ids` varchar(1000) DEFAULT NULL,
  `dashboards` varchar(1000) DEFAULT NULL,
  `report_ids` varchar(1000) DEFAULT NULL,
  `is_system_role` varchar(5) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `role_id` (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sector`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sector` (
  `sector_id` int NOT NULL AUTO_INCREMENT,
  `sector_code` varchar(5) DEFAULT NULL,
  `sector` varchar(100) DEFAULT NULL,
  `captured_by` varchar(50) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`sector_id`)
) ENGINE=MyISAM AUTO_INCREMENT=17 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `security`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `security` (
  `security_id` int NOT NULL AUTO_INCREMENT,
  `account_lockout_duration` varchar(20) DEFAULT NULL,
  `account_unlock_duration` varchar(20) DEFAULT NULL,
  `account_lockout_threshold` varchar(20) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`security_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_type`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staff_type` (
  `staff_type_id` int NOT NULL AUTO_INCREMENT,
  `staff_type` varchar(100) DEFAULT NULL,
  `captured_by` varchar(50) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`staff_type_id`)
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `system`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `system` (
  `system_id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(100) DEFAULT NULL,
  `technical_support_contact` varchar(100) DEFAULT NULL,
  `technical_support_telephone` varchar(100) DEFAULT NULL,
  `technical_support_email` varchar(100) DEFAULT NULL,
  `licensee` varchar(100) DEFAULT NULL,
  `licensee_short_name` varchar(100) DEFAULT NULL,
  `slogan` varchar(100) DEFAULT NULL,
  `address` varchar(200) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `website` varchar(50) DEFAULT NULL,
  `telephone` varchar(50) DEFAULT NULL,
  `landing_page_id` varchar(20) DEFAULT NULL,
  `registration_number_prefix` varchar(20) DEFAULT NULL,
  `registration_number_format` varchar(200) DEFAULT NULL,
  `invoice_number_prefix` varchar(20) DEFAULT NULL,
  `invoice_number_format` varchar(200) DEFAULT NULL,
  `receipt_number_prefix` varchar(20) DEFAULT NULL,
  `receipt_number_format` varchar(200) DEFAULT NULL,
  `organization_disable_duration` varchar(20) DEFAULT '0',
  `reporting_period_start_date` datetime DEFAULT NULL,
  `reporting_period_end_date` datetime DEFAULT NULL,
  `users_per_ngo` varchar(20) DEFAULT NULL,
  `submission_acceptance_status` varchar(5) DEFAULT NULL,
  `submission_deadline` varchar(5) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`system_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `target_group`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `target_group` (
  `target_group_id` int NOT NULL AUTO_INCREMENT,
  `target_group_code` varchar(5) DEFAULT NULL,
  `target_group` varchar(100) DEFAULT NULL,
  `captured_by` varchar(50) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(50) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(50) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`target_group_id`)
) ENGINE=MyISAM AUTO_INCREMENT=9 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `template`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `template` (
  `template_id` int NOT NULL AUTO_INCREMENT,
  `template` varchar(100) DEFAULT NULL,
  `text1` varchar(500) DEFAULT NULL,
  `text1_type` varchar(5) DEFAULT NULL,
  `text2` varchar(500) DEFAULT NULL,
  `text2_type` varchar(5) DEFAULT NULL,
  `text3` varchar(500) DEFAULT NULL,
  `text3_type` varchar(5) DEFAULT NULL,
  `text4` varchar(500) DEFAULT NULL,
  `text4_type` varchar(5) DEFAULT NULL,
  `text5` varchar(500) DEFAULT NULL,
  `text5_type` varchar(5) DEFAULT NULL,
  `text6` varchar(500) DEFAULT NULL,
  `text6_type` varchar(5) DEFAULT NULL,
  `text7` varchar(500) DEFAULT NULL,
  `text7_type` varchar(5) DEFAULT NULL,
  `text8` varchar(500) DEFAULT NULL,
  `text8_type` varchar(5) DEFAULT NULL,
  `text9` varchar(500) DEFAULT NULL,
  `text9_type` varchar(5) DEFAULT NULL,
  `text10` varchar(500) DEFAULT NULL,
  `text10_type` varchar(5) DEFAULT NULL,
  `text11` varchar(500) DEFAULT NULL,
  `text11_type` varchar(5) DEFAULT NULL,
  `text12` varchar(500) DEFAULT NULL,
  `text12_type` varchar(5) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `template_id` (`template_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `template_20231030`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `template_20231030` (
  `template_id` int NOT NULL AUTO_INCREMENT,
  `template` varchar(100) DEFAULT NULL,
  `text1` varchar(500) DEFAULT NULL,
  `text1_type` varchar(5) DEFAULT NULL,
  `text2` varchar(500) DEFAULT NULL,
  `text2_type` varchar(5) DEFAULT NULL,
  `text3` varchar(500) DEFAULT NULL,
  `text3_type` varchar(5) DEFAULT NULL,
  `text4` varchar(500) DEFAULT NULL,
  `text4_type` varchar(5) DEFAULT NULL,
  `text5` varchar(500) DEFAULT NULL,
  `text5_type` varchar(5) DEFAULT NULL,
  `text6` varchar(500) DEFAULT NULL,
  `text6_type` varchar(5) DEFAULT NULL,
  `text7` varchar(500) DEFAULT NULL,
  `text7_type` varchar(5) DEFAULT NULL,
  `text8` varchar(500) DEFAULT NULL,
  `text8_type` varchar(5) DEFAULT NULL,
  `text9` varchar(500) DEFAULT NULL,
  `text9_type` varchar(5) DEFAULT NULL,
  `text10` varchar(500) DEFAULT NULL,
  `text10_type` varchar(5) DEFAULT NULL,
  `text11` varchar(500) DEFAULT NULL,
  `text11_type` varchar(5) DEFAULT NULL,
  `text12` varchar(500) DEFAULT NULL,
  `text12_type` varchar(5) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `template_id` (`template_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tep`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tep` (
  `tep_id` int NOT NULL AUTO_INCREMENT,
  `organization_id` varchar(20) DEFAULT NULL,
  `fullname` varchar(100) DEFAULT NULL,
  `nationality` varchar(50) DEFAULT NULL,
  `passport_number` varchar(20) DEFAULT NULL,
  `invoice_number` varchar(20) DEFAULT NULL,
  `payment_proof` varchar(100) DEFAULT NULL,
  `record_control` varchar(5) DEFAULT NULL,
  `payment_processed_by` varchar(20) DEFAULT NULL,
  `payment_processed_date` datetime DEFAULT NULL,
  `approved1_by` varchar(20) DEFAULT NULL,
  `approved1_date` datetime DEFAULT NULL,
  `approved2_by` varchar(20) DEFAULT NULL,
  `approved2_date` datetime DEFAULT NULL,
  `rejected_comments` varchar(200) DEFAULT NULL,
  `rejected_by` varchar(20) DEFAULT NULL,
  `rejected_date` datetime DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `tep_id` (`tep_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=151 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `thematic_area`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `thematic_area` (
  `thematic_area_id` int NOT NULL AUTO_INCREMENT,
  `thematic_area` varchar(100) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  UNIQUE KEY `sector_id` (`thematic_area_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user` (
  `user_id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(20) DEFAULT NULL,
  `firstname` varchar(100) DEFAULT NULL,
  `lastname` varchar(100) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `position` varchar(50) DEFAULT NULL,
  `organization_id` varchar(20) DEFAULT NULL,
  `password` varchar(100) DEFAULT NULL,
  `district_id` varchar(20) DEFAULT NULL,
  `role_id` varchar(20) DEFAULT NULL,
  `photo` varchar(50) DEFAULT NULL,
  `log_attempts` varchar(4) DEFAULT NULL,
  `change_password` varchar(5) DEFAULT NULL,
  `account_disabled` varchar(5) DEFAULT NULL,
  `account_locked` varchar(5) DEFAULT NULL,
  `account_lockout_time` varchar(50) DEFAULT NULL,
  `is_ngo_user` varchar(5) DEFAULT NULL,
  `is_system_user` varchar(5) DEFAULT NULL,
  `email_verified` varchar(5) DEFAULT NULL,
  `email_verification_key` varchar(100) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2970 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zone`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zone` (
  `zone_id` int NOT NULL AUTO_INCREMENT,
  `zone_name` varchar(50) DEFAULT NULL,
  `region_id` varchar(20) DEFAULT NULL,
  `captured_by` varchar(20) DEFAULT NULL,
  `captured_date` datetime DEFAULT NULL,
  `last_edited_by` varchar(20) DEFAULT NULL,
  `last_edited_date` datetime DEFAULT NULL,
  `deleted_by` varchar(20) DEFAULT NULL,
  `deleted_date` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`zone_id`)
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-01 12:01:07