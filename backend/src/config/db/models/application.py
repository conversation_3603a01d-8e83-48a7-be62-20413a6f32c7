from enum import Enum

from sqlalchemy import Column, String
from sqlalchemy import Enum as SQLEnum
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class ApplicationType(str, Enum):
	ORGANIZATION_REGISTRATION = "ORGANIZATION_REGISTRATION"
	LICENCE_RENEWAL = "LICENCE_RENEWAL"
	PERMIT_APPLICATION = "PERMIT_APPLICATION"


class ApplicationStatus(str, Enum):
	DRAFT = "DRAFT"
	REVIEW = "REVIEW"
	REJECTED = "REJECTED"
	SUSPENDED = "SUSPENDED"
	REGISTERED = "REGISTERED"


class Application(BaseModel, AuditMixin):
	__tablename__ = tables.applications

	id = primary_key()
	type = Column(
		SQLEnum(ApplicationType),
		nullable=False,
		default=ApplicationType.ORGANIZATION_REGISTRATION,
	)
	status = Column(SQLEnum(ApplicationStatus), nullable=False, default=ApplicationStatus.DRAFT)
	code = Column(String(15), nullable=False, unique=True)
	organization_id = foreign_key(f"{tables.organizations}.id")

	# Relationships
	workflows = relationship(
		"Workflow",
		back_populates="application",
		cascade="all, delete-orphan",
		foreign_keys="Workflow.application_id",
	)
	organization = relationship(
		"Organization",
		back_populates="applications",
		uselist=False,
		foreign_keys=[organization_id],
	)
	application_fees = relationship(
		"ApplicationFee",
		back_populates="application",
		cascade="all, delete-orphan",
		foreign_keys="ApplicationFee.application_id",
	)
	documents = relationship(
		"ApplicationDocument",
		back_populates="application",
		uselist=True,
		foreign_keys="ApplicationDocument.application_id",
		cascade="all, delete-orphan",
	)
	invoice_items = relationship(
		"InvoiceItem",
		back_populates="application",
		uselist=True,
		foreign_keys="InvoiceItem.application_id",
		cascade="all, delete-orphan",
	)

	def __repr__(self):
		return f"<Application(id={self.id}, type={self.type}, status={self.status}, code={self.code})>"
