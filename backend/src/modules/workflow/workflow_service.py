from typing import List

from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from pydantic import UUID4
from sqlalchemy import and_, or_
from sqlalchemy.orm import Session as OrmSession

from src.config.db.models import Template
from src.config.db.models.template_stage import TemplateStage
from src.config.db.models.template_stage_role import TemplateStageRole
from src.config.db.models.template_stage_trigger import TemplateStageTrigger
from src.core.base.base_repository import BaseRepository
from src.core.dtos.workflow_dtos import (
	TemplateDto,
	TemplateStageDto,
	TemplateStageRoleDto,
	TemplateStageTriggerDto,
	to_template_dto,
	to_template_stage_dto,
	to_template_stage_role_dto,
	to_template_stage_trigger_dto,
)
from src.core.exceptions.api import ApiException
from src.core.logger.internal_logger import get_logger
from src.core.shared_schema import Pagination, VoidRequest
from src.core.utils.common import capitalize_words
from src.modules.workflow.workflow_schema import (
	TemplateFilter,
	TemplateRequest,
	TemplateStageFilter,
	TemplateStageRequest,
	TemplateStageRoleFilter,
	TemplateStageRoleRequest,
	TemplateStageTriggerFilter,
	TemplateStageTriggerRequest,
)


class WorkflowService(BaseRepository):
	"""
	Service class for managing workflow templates.
	Inherits from BaseRepository for database operations.
	"""

	def __init__(self, db: OrmSession = None):
		if db is not None:
			self.db = db
		else:
			super().__init__()

		self.logger = get_logger(__name__)

	async def retrieve_templates(self, filter: TemplateFilter) -> Pagination[TemplateDto]:
		try:
			query = self.db.query(Template)

			if filter.name:
				query = query.filter(Template.name.ilike(f"%{filter.name}%"))

			query_result = paginate(query, Params(page=filter.page, size=filter.size))
			data = [to_template_dto(row) for row in query_result.items]

			return Pagination.from_query_result(data, query_result.total, query_result.page, query_result.size)
		except Exception as e:
			self.logger.error(f"Failed to retrieve workflow template: {e}")
			raise

	async def create_template(self, payload: TemplateRequest) -> TemplateDto:
		try:
			with self.db.begin_nested():
				existing_template = (
					self.db.query(Template)
					.filter(or_(Template.name == payload.name, Template.code == payload.code))
					.first()
				)

				if existing_template:
					raise ApiException("Workflow template with the same code or name already exists")

				template = Template(
					name=capitalize_words(payload.name),
					code=payload.code.upper(),
					description=payload.description,
					created_by=self.current_user.id,
				)

				self.db.add(template)
				self.db.flush()

				await self.add_template_stage_roles(template.id, payload.roles)
				await self.add_template_stage_triggers(template.id, payload.triggers)

				self.db.commit()
				self.db.refresh(template)

				return to_template_dto(template)
		except ApiException:
			self.db.rollback()
			raise
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to create workflow template: {e}")
			raise

	async def update_template(self, template_id: UUID4, payload: TemplateRequest) -> TemplateDto:
		try:
			template = self.db.query(Template).filter(Template.id == template_id).first()

			if not template:
				raise ApiException("Workflow template does not exists")

			template.name = capitalize_words(payload.name)
			template.description = payload.description
			template.updated_by = self.current_user.id

			self.db.add(template)
			self.db.commit()
			self.db.refresh(template)

			return to_template_dto(template)
		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to update workflow template: {e}")
			raise

	async def void_template(self, template_id: UUID4, payload: VoidRequest) -> bool:
		try:
			template = self.db.query(Template).filter(Template.id == template_id).first()

			if not template:
				raise ApiException("Workflow template does not exists")

			template.voided_by = self.current_user.id
			template.void_reason = payload.void_reason

			self.db.add(template)
			self.db.commit()
			self.db.refresh(template)

			return True
		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to void workflow template: {e}")
			raise

	async def add_template_stage_roles(
		self, stage_id: UUID4, roles: List[TemplateStageRoleRequest]
	) -> List[TemplateStageRoleDto]:
		try:
			stage = self.db.query(TemplateStage).filter(TemplateStage.id == stage_id).first()

			if not stage:
				raise ApiException("Workflow template stage does not exists")

			new_roles = []
			for role_id in roles:
				existing_role = (
					self.db.query(TemplateStageRole)
					.filter(and_(TemplateStageRole.template_stage_id == stage_id, TemplateStageRole.role_id == role_id))
					.first()
				)
				if existing_role:
					raise ApiException(f"Role with ID '{role_id}' already exists for this stage")

				new_role = TemplateStageRole(
					template_stage_id=stage_id,
					role_id=role_id,
					is_active=True,
					created_by=self.current_user.id,
				)
				new_roles.append(new_role)

			self.db.add_all(new_roles)
			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return [to_template_stage_role_dto(role) for role in new_roles]
		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to add roles to workflow template stage: {e}")
			raise

	async def add_template_stage_triggers(
		self, stage_id: UUID4, triggers: List[TemplateStageTriggerRequest]
	) -> List[TemplateStageTriggerDto]:
		try:
			stage = self.db.query(TemplateStage).filter(TemplateStage.id == stage_id).first()

			if not stage:
				raise ApiException("Workflow template stage does not exists")

			new_triggers = []
			for trigger in triggers:
				existing_trigger = (
					self.db.query(TemplateStageTrigger)
					.filter(
						and_(
							TemplateStageTrigger.template_stage_id == stage_id,
							TemplateStageTrigger.function_id == trigger.function_id,
						)
					)
					.first()
				)
				if existing_trigger:
					raise ApiException(
						f"Trigger with ID '{existing_trigger.function.display_value}' already exists for this stage"
					)

				new_trigger = TemplateStageTrigger(
					template_stage_id=stage_id,
					function_id=trigger.function_id,
					action_mode=trigger.action_mode,
					created_by=self.current_user.id,
				)
				new_triggers.append(new_trigger)

			self.db.add_all(new_triggers)
			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return [to_template_stage_trigger_dto(trigger) for trigger in new_triggers]
		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to add triggers to workflow template stage: {e}")
			raise

	async def retrieve_template_stages(
		self, template_id: UUID4, filter: TemplateStageFilter
	) -> Pagination[TemplateStageDto]:
		try:
			query = self.db.query(TemplateStage).filter(TemplateStage.template_id == template_id)

			if filter.name:
				query = query.filter(TemplateStage.name.ilike(f"%{filter.name}%"))

			if filter.is_active:
				query = query.filter(TemplateStage.is_active == filter.is_active)

			query = query.order_by(TemplateStage.position)

			query_result = paginate(query, Params(page=filter.page, size=filter.size))
			data = [to_template_stage_dto(row) for row in query_result.items]
			return Pagination.from_query_result(data, query_result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve template stages: {e}")
			raise

	async def update_template_stage(self, stage_id: UUID4, payload: TemplateStageRequest) -> TemplateStageDto:
		try:
			stage = self.db.query(TemplateStage).filter(TemplateStage.id == stage_id).first()

			if not stage:
				raise ApiException("Workflow template stage does not exists")

			current_position = stage.position
			new_position = payload.position

			total_stages = self.db.query(TemplateStage).filter(TemplateStage.template_id == stage.template_id).count()

			if new_position > total_stages:
				new_position = total_stages
			elif new_position < 1:
				new_position = 1

			if current_position != new_position:
				if new_position > current_position:
					self.db.query(TemplateStage).filter(
						and_(
							TemplateStage.template_id == stage.template_id,
							TemplateStage.position > current_position,
							TemplateStage.position <= new_position,
						)
					).update({TemplateStage.position: TemplateStage.position - 1})
				else:
					self.db.query(TemplateStage).filter(
						and_(
							TemplateStage.template_id == stage.template_id,
							TemplateStage.position >= new_position,
							TemplateStage.position < current_position,
						)
					).update({TemplateStage.position: TemplateStage.position + 1})

			stage.name = capitalize_words(payload.name)
			stage.description = payload.description
			stage.is_active = payload.is_active
			stage.position = new_position
			stage.updated_by = self.current_user.id

			self.db.add(stage)
			self.db.commit()
			self.db.refresh(stage)

			return to_template_stage_dto(stage)

		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to update workflow template stage: {e}")
			raise

	async def void_template_stage(self, stage_id: UUID4, payload: VoidRequest) -> bool:
		try:
			stage = self.db.query(TemplateStage).filter(TemplateStage.id == stage_id).first()

			if not stage:
				raise ApiException("Workflow template stage does not exists")

			stage.voided = True
			stage.voided_by = self.current_user.id
			stage.void_reason = payload.void_reason

			self.db.add(stage)
			self.db.commit()
			self.db.refresh(stage)

			return True
		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to void workflow template stage: {e}")
			raise

	async def retrieve_template_stage_roles(
		self, stage_id: UUID4, filter: TemplateStageRoleFilter
	) -> Pagination[TemplateStageRoleDto]:
		try:
			query = self.db.query(TemplateStageRole).filter(TemplateStageRole.template_stage_id == stage_id)

			if filter.is_active:
				query = query.filter(TemplateStageRole.is_active == filter.is_active)

			query_result = paginate(query, Params(page=filter.page, size=filter.size))
			data = [to_template_stage_role_dto(row) for row in query_result.items]

			return Pagination.from_query_result(data, query_result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve template stage roles: {e}")
			raise

	async def void_template_stage_role(self, stage_role_id: UUID4, payload: VoidRequest) -> bool:
		try:
			role = self.db.query(TemplateStageRole).filter(TemplateStageRole.id == stage_role_id).first()

			if not role:
				raise ApiException("Template stage role does not exists")

			role.voided = True
			role.voided_by = self.current_user.id
			role.void_reason = payload.void_reason

			self.db.add(role)
			self.db.commit()
			self.db.refresh(role)

			return True
		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to void template stage role: {e}")
			raise

	async def retrieve_template_stage_triggers(
		self, stage_id: UUID4, filter: TemplateStageTriggerFilter
	) -> Pagination[TemplateStageTriggerDto]:
		try:
			query = self.db.query(TemplateStageTrigger).filter(TemplateStageTrigger.template_stage_id == stage_id)

			if filter.action_mode:
				query = query.filter(TemplateStageTrigger.action_mode == filter.action_mode)

			query_result = paginate(query, Params(page=filter.page, size=filter.size))
			data = [to_template_stage_trigger_dto(row) for row in query_result.items]

			return Pagination.from_query_result(data, query_result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve template stage triggers: {e}")
			raise

	async def void_template_stage_trigger(self, stage_trigger_id: UUID4, payload: VoidRequest) -> bool:
		try:
			trigger = self.db.query(TemplateStageTrigger).filter(TemplateStageTrigger.id == stage_trigger_id).first()

			if not trigger:
				raise ApiException("Template stage trigger does not exists")

			trigger.voided = True
			trigger.voided_by = self.current_user.id
			trigger.void_reason = payload.void_reason

			self.db.add(trigger)
			self.db.commit()
			self.db.refresh(trigger)

			return True
		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to void template stage trigger: {e}")
			raise

	async def add_template_stage(
		self, template_id: UUID4, payload: (TemplateStageRequest | List[TemplateStageRequest])
	) -> List[TemplateStageDto]:
		try:
			template = self.db.query(Template).filter(Template.id == template_id).first()

			if not template:
				raise ApiException("Workflow template does not exists")

			stages_to_create = payload if isinstance(payload, list) else [payload]

			new_stages = []
			for stage in stages_to_create:
				existing_stage = (
					self.db.query(TemplateStage)
					.filter(
						or_(
							and_(TemplateStage.name == stage.name, TemplateStage.template_id == template_id),
							and_(TemplateStage.template_id == template_id, TemplateStage.position == stage.position),
						)
					)
					.first()
				)
				if existing_stage:
					raise ApiException(f"Stage with name '{stage.name}' or position '{stage.position}' already exists")

				new_stages.append(
					TemplateStage(
						template_id=template_id,
						name=capitalize_words(stage.name),
						description=stage.description,
						is_active=stage.is_active,
						position=stage.position,
						created_by=self.current_user.id,
					)
				)

			self.db.add_all(new_stages)
			self.db.flush()
			self.db.commit()

			return [to_template_stage_dto(stage) for stage in new_stages]
		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to add stage to workflow template: {e}")
			raise


workflow_service = WorkflowService()
