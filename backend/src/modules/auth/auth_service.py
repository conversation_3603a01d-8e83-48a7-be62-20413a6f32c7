import hashlib
import random
import secrets
import string
from datetime import datetime, timed<PERSON>ta
from typing import Dict, <PERSON>, Optional, Tu<PERSON>

from fastapi import Request
from fastapi.security import OAuth2Password<PERSON>earer
from sqlalchemy.orm import joinedload

from src.config import settings
from src.config.data.adjectives import adjectives
from src.config.db.models import (
	Account,
	AccountStatus,
	AccountType,
	EmailVerification,
	LoadableItem,
	RolePermission,
	Session,
	User,
	VerificationType,
)
from src.config.settings import CURRENT_USER_TTL, ROLE_PERMISSION_TTL, SESSION_TYPE_KEY_COOKIE
from src.core.base.base_repository import BaseRepository
from src.core.dtos.auth_dtos import AuthDto, AuthType
from src.core.exceptions.api import ApiException
from src.core.logger import logger
from src.core.services.cache_service import cache_service
from src.core.services.email_service import EmailMessage, EmailRecipient, EmailTemplate, email_service
from src.core.services.encryption_service import EncryptionService
from src.core.shared_schema import CurrentUser
from src.modules.auth.auth_schema import (
	EmailVerificationRequest,
	LoginRequest,
	RegistrationRequest,
	SessionInfo,
	VerificationRetryRequest,
)


class AuthService(BaseRepository):
	def __init__(self):
		super().__init__()
		self.oauth2_scheme = OAuth2PasswordBearer(tokenUrl="v1/auth/login")
		self.token_expiry = settings.ACCESS_TOKEN_EXPIRE_MINUTES
		self.encryption_service = EncryptionService()
		self.cache_service = cache_service
		self.email_service = email_service

		self.REMEMBER_ME_DAYS = 30
		self.REGULAR_SESSION_DAYS = 7
		self.REMEMBER_ME_TOKEN_DAYS = 30
		self.REGULAR_TOKEN_HOURS = 24

	def _generate_session_token(self) -> str:
		"""Generate a unique session token"""
		return secrets.token_urlsafe(32)

	def _hash_token(self, token: str) -> str:
		"""Hash a token for secure storage"""
		return hashlib.sha256(token.encode()).hexdigest()

	async def generate_username(self, retries: int = 20) -> str:
		MAX_USERNAME_LENGTH = 15
		for _ in range(retries):
			choice = random.choice(adjectives)
			number = str(random.randint(100, 999))

			username = f"{choice}_{number}".lower()

			if len(username) > MAX_USERNAME_LENGTH:
				continue

			exists = self.db.query(Account).filter(Account.handle == username).first()
			if not exists:
				return username

		raise Exception("Failed to generate a unique username under 15 characters after several attempts.")

	def _extract_device_info(self, user_agent: str) -> str:
		"""Extract basic device info from user agent"""
		if not user_agent:
			return "Unknown"

		user_agent_lower = user_agent.lower()
		if "mobile" in user_agent_lower or "android" in user_agent_lower or "iphone" in user_agent_lower:
			return "Mobile"
		elif "tablet" in user_agent_lower or "ipad" in user_agent_lower:
			return "Tablet"
		else:
			return "Desktop"

	def _create_session(self, user: User, request: Request, refresh_token: str, remember_me: bool = False) -> Session:
		"""Create a new user session"""
		session_token = self._generate_session_token()
		refresh_token_hash = self._hash_token(refresh_token)

		ip_address = request.client.host if request.client else "Unknown"
		user_agent = request.headers.get("user-agent", "")
		device = self._extract_device_info(user_agent)

		if remember_me:
			expires_at = datetime.now() + timedelta(days=self.REMEMBER_ME_DAYS)
		else:
			expires_at = datetime.now() + timedelta(days=self.REGULAR_SESSION_DAYS)

		session = Session(
			user_id=user.id,
			session_token=session_token,
			refresh_token_hash=refresh_token_hash,
			device=device,
			ip_address=ip_address,
			user_agent=user_agent,
			expires_at=expires_at,
			is_active=True,
			last_activity=datetime.utcnow(),
			remember_me=remember_me,
		)

		self.db.add(session)
		self.db.commit()
		return session

	def _generate_auth_tokens(self, user: User, remember_me: bool = False) -> Dict[str, str]:
		if remember_me:
			access_token = timedelta(hours=24)
			refresh_token = timedelta(days=self.REMEMBER_ME_TOKEN_DAYS)
		else:
			access_token = timedelta(minutes=self.token_expiry)
			refresh_token = timedelta(days=self.REGULAR_TOKEN_HOURS)

		token_data = {
			"sub": user.account.handle,
			"user_id": str(user.id),
			"email": user.email,
			"remember_me": remember_me,
		}

		return self.encryption_service.generate_tokens(
			user_id=user.id,
			additional_data=token_data,
			access_token=access_token,
			refresh_token=refresh_token,
		)

	async def get_current_user(self, token: str) -> CurrentUser | None:
		"""
		Validate token and return current user with caching
		"""
		is_valid, payload = self.encryption_service.verify_token(token)

		if not is_valid or payload.get("type") != "access":
			return None

		user_id = payload.get("user_id")
		if user_id is None:
			return None

		cached_user = await self.cache_service.get(f"user:{user_id}")
		if cached_user:
			return CurrentUser(**cached_user)

		user = self.db.query(User).join(Account, User.account).filter(User.id == user_id).first()

		if not user:
			return None

		if user.account.status != AccountStatus.ACTIVE.value and user.verified:
			return None

		role_id = str(user.roles[0].id) if hasattr(user, "roles") and user.roles else None
		role_code = user.roles[0].code if hasattr(user, "roles") and user.roles else None

		current_user = CurrentUser(
			id=str(user.id),
			username=user.account.handle,
			role_id=role_id,
			role_code=role_code,
		)

		print(current_user.model_dump())

		# await self.cache_service.set(f"user:{user_id}", current_user.model_dump(), expire=3600)

		return current_user

	def get_user_by_id(self, user_id: str) -> User | None:
		"""
		Get user by ID with account relationship loaded
		"""
		if not user_id:
			return None

		return self.db.query(User).options(joinedload(User.account)).filter(User.id == user_id).first()

	def _update_session_activity(self, user_id: str):
		"""Update last activity for user sessions"""
		try:
			self.db.query(Session).filter(Session.user_id == user_id, Session.is_active).update(
				{Session.last_activity: datetime.utcnow()}
			)
			self.db.commit()
		except Exception as e:
			logger.warning(f"Failed to update session activity: {str(e)}")

	async def _get_role_permissions(self, role_id: str) -> List[str]:
		try:
			rows = (
				self.db.query(LoadableItem)
				.join(RolePermission, RolePermission.permission_id == LoadableItem.id)
				.filter(RolePermission.role_id == role_id)
				.all()
			)

			return [row.code.lower() for row in rows]
		except Exception as e:
			self.logger.error(f"Failed to get role permissions: {str(e)}")
			raise

	async def cache_role_permissions_cache(self, role_id: str = None, role_code: str = None):
		if role_id and role_code:
			permissions = await self._get_role_permissions(role_id)
			await self.cache_service.set(f"role_permissions:{role_code}", permissions, expire=ROLE_PERMISSION_TTL)

	async def authenticate_user(self, payload: LoginRequest, request: Request):
		"""Authenticate user and create session"""
		try:
			query = self.db.query(User).join(Account, User.account)
			user = None

			if "@" in payload.identifier:
				user = query.filter(User.email == payload.identifier).first()
			else:
				user = query.filter(Account.handle == payload.identifier).first()

			if not user:
				raise ApiException("Could find user with such email or username")

			if not self.encryption_service.verify_password(payload.password, user.hashed_password):
				raise ApiException("Incorrect password")

			if user.account.status == AccountStatus.INACTIVE.value and user.verified:
				raise ApiException("Account is inactive, please contact the administrator")

			if payload.remember_me:
				self._cleanup_old_sessions(user.id, keep_active=True)

			auth_tokens = self._generate_auth_tokens(user, payload.remember_me)

			self._create_session(user, request, auth_tokens["refresh_token"], payload.remember_me)

			current_user_data = {
				"id": str(user.id),
				"username": user.account.handle,
				"role_id": None,
				"role_code": None,
			}

			if not user.is_external and user.verified:
				role = user.roles[0] if user.roles else None
				if role:
					await self.cache_role_permissions_cache(role.id, role.code)
					current_user_data["role_id"] = str(role.id)
					current_user_data["role_code"] = role.code

			if user.verified:
				await self.cache_service.set(f"user:{user.id}", current_user_data, expire=CURRENT_USER_TTL)

			return auth_tokens, AuthDto(
				first_name=user.first_name,
				last_name=user.last_name,
				username=user.account.handle,
				email=user.email,
				gender=user.gender,
				user_id=user.id,
				auth_type=AuthType.MAIN if user.verified else AuthType.VERIFICATION,
				name=f"{user.first_name} {user.last_name}",
			)
		except Exception as e:
			logger.error(f"Authentication error: {str(e)}")
			raise ApiException(str(e))

	def _cleanup_old_sessions(self, user_id: str, keep_active: bool = False):
		"""Clean up old sessions for a user"""
		try:
			current_time = datetime.now()

			query = self.db.query(Session).filter(Session.user_id == user_id)

			if keep_active:
				query = query.filter((~Session.is_active) | (Session.expires_at <= current_time))

			old_sessions = query.all()

			for session in old_sessions:
				if session.is_active:
					session.is_active = False
					session.logged_out_at = current_time
				self.db.add(session)

			self.db.commit()

			if old_sessions:
				logger.info(f"Cleaned up {len(old_sessions)} old sessions for user {user_id}")

		except Exception as e:
			logger.error(f"Session cleanup error: {str(e)}")
			self.db.rollback()

	def logout_user(self, token: str, all_sessions: bool = False, session_id: Optional[str] = None) -> Tuple[bool, int]:
		"""
		Logout user by invalidating sessions
		Returns: (success, number_of_sessions_logged_out)
		"""
		try:
			# Get user from token
			is_valid, payload = self.encryption_service.verify_token(token)
			if not is_valid:
				return False, 0

			user_id = payload.get("user_id")
			if not user_id:
				return False, 0

			current_time = datetime.now()

			if all_sessions:
				sessions_to_logout = self.db.query(Session).filter(Session.user_id == user_id, Session.is_active).all()

				sessions_count = len(sessions_to_logout)

				for session in sessions_to_logout:
					session.is_active = False
					session.logged_out_at = current_time
					self.db.add(session)

			else:
				if session_id:
					session_to_logout = (
						self.db.query(Session)
						.filter(Session.user_id == user_id, Session.session_token == session_id, Session.is_active)
						.first()
					)
				else:
					# Get the most recently active session
					session_to_logout = (
						self.db.query(Session)
						.filter(Session.user_id == user_id, Session.is_active)
						.order_by(Session.last_activity.desc())
						.first()
					)

				if session_to_logout:
					session_to_logout.is_active = False
					session_to_logout.logged_out_at = current_time
					self.db.add(session_to_logout)
					sessions_count = 1
				else:
					sessions_count = 0

			self.db.commit()
			return True, sessions_count

		except Exception as e:
			logger.error(f"Logout error: {str(e)}")
			self.db.rollback()
			return False, 0

	def get_user_sessions(self, user_id: str, current_session_token: Optional[str] = None) -> list[SessionInfo]:
		"""Get all active sessions for a user"""
		sessions = (
			self.db.query(Session)
			.filter(Session.user_id == user_id, Session.is_active, Session.expires_at > datetime.utcnow())
			.order_by(Session.last_activity.desc())
			.all()
		)

		session_infos = []
		for session in sessions:
			session_infos.append(
				SessionInfo(
					session_id=session.session_token,
					ip_address=session.ip_address,
					user_agent=session.user_agent,
					device=session.device,
					created_at=session.created_at,
					last_activity=session.last_activity,
					is_current=(session.session_token == current_session_token),
					remember_me=session.remember_me,
					expires_at=session.expires_at,
				)
			)

		return session_infos

	def cleanup_expired_sessions(self):
		"""Clean up expired sessions - call this periodically"""
		try:
			current_time = datetime.now()

			expired_sessions = (
				self.db.query(Session).filter(Session.expires_at <= current_time, Session.is_active).all()
			)

			remember_me_count = 0
			regular_count = 0

			for session in expired_sessions:
				session.is_active = False
				session.logged_out_at = current_time
				self.db.add(session)

			if session.remember_me:
				remember_me_count += 1
			else:
				regular_count += 1

			self.db.commit()
			logger.info(
				f"Cleaned up {len(expired_sessions)} expired sessions"
				f"({remember_me_count} remember me, {regular_count} regular)"
			)

		except Exception as e:
			logger.error(f"Session cleanup error: {str(e)}")
			self.db.rollback()

	def refresh_access_token(self, refresh_token: str) -> Dict[str, str]:
		"""
		Use a refresh token to generate a new access token
		"""
		is_valid, payload = self.encryption_service.verify_token(refresh_token)

		if not is_valid or payload.get("type") != "refresh":
			raise ApiException("Invalid refresh token")

		user_id: str = payload.get("user_id")
		remember_me = payload.get("remember_me", False)

		refresh_token_hash = self._hash_token(refresh_token)
		session = (
			self.db.query(Session)
			.filter(
				Session.user_id == user_id,
				Session.refresh_token_hash == refresh_token_hash,
				Session.is_active,
				Session.expires_at > datetime.utcnow(),
			)
			.first()
		)

		if not session:
			raise ApiException("Session expired or invalid")

		user = self.db.query(User).join(Account, User.account).where(User.id == user_id).first()

		if not user or user.account.status != AccountStatus.ACTIVE:
			raise ApiException("User was not found or inactive")

			# Update session activity
		session.last_activity = datetime.utcnow()
		self.db.add(session)
		self.db.commit()

		token_data = {
			"sub": user.account.handle,
			"user_id": str(user.id),
			"remember_me": remember_me,
			"email": user.email,
		}

		if remember_me:
			# If remember me is true, set longer expiry for access token
			access_token_expires = timedelta(hours=24)
		else:
			# Default expiry for access token is 15 minutes
			access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

		access_token = self.encryption_service.create_access_token(token_data, expires_delta=access_token_expires)

		return {"access_token": access_token, "token_type": "bearer", "remember_me": remember_me}

	def initiate_password_reset(self, email: str) -> str:
		"""
		Create a password reset token and return it
		In a real app, you would send this via email
		"""
		try:
			user = self.db.query(User).where(User.email == email).first()

			if not user:
				logger.warning(f"Password reset requested for non-existent email: {email}")
				return True

			reset_token = self.encryption_service.generate_password_reset_token(email)

			reset_url = f"{settings.BACKEND_URL}/v1/auth/reset-password?token={reset_token}"

			email_message = EmailMessage(
				template=EmailTemplate.RESET_PASSWORD,
				recipients=[EmailRecipient(email=email, name=f"{user.first_name} {user.last_name}")],
				subject=f"Password Reset Request for {settings.APP_NAME}",
				sender_email=settings.SENDER_EMAIL,
				sender_name=settings.SENDER_NAME,
				template_variables={
					"user_name": user.first_name,
					"reset_url": reset_url,
					"app_name": settings.APP_NAME,
					"year": datetime.now().year,
				},
			)

			self.email_service.send_email(email_message)

			logger.info(f"Password reset email for {email}")
			return True

		except Exception as e:
			logger.error(f"Password reset error for {email}: {str(e)}")
			return True

	def validate_password_reset_token(self, token: str) -> Tuple[bool, Optional[str]]:
		"""Validate password reset token and return email if valid"""
		try:
			email = self.encryption_service.verify_password_reset_token(token)
			if email:
				user = self.db.query(User).filter(User.email == email).first()
				if user:
					return True, email
			return False, None
		except Exception:
			logger.error(f"Invalid password reset token: {token}")
			return False, None

	def complete_password_reset(self, token: str, new_password: str) -> bool:
		"""
		Complete the password reset process by updating the user's password
		"""
		try:
			email = self.encryption_service.verify_password_reset_token(token)
			if not email:
				raise ApiException("Invalid or expired password reset token")

			user = self.db.query(User).filter(User.email == email).first()
			if not user:
				raise ApiException("User not found")

			hashed_password = self.encryption_service.hash_password(new_password)
			user.hashed_password = hashed_password
			user.updated_at = datetime.utcnow()

			# Invalidate all existing sessions for security
			current_time = datetime.utcnow()
			active_sessions = self.db.query(Session).filter(Session.user_id == user.id, Session.is_active).all()

			for session in active_sessions:
				session.is_active = False
				session.logged_out_at = current_time
				self.db.add(session)

			self.db.add(user)
			self.db.commit()

			self._send_password_reset_confirmation(user)
			logger.info(f"Password reset completed for user: {email}")
			return True

		except Exception as e:
			logger.error(f"Password reset error: {str(e)}")
			self.db.rollback()
			raise ApiException("Failed to complete password reset process")

	def _send_password_reset_confirmation(self, user: User):
		"""Send confirmation email after password reset"""
		try:
			email_message = EmailMessage(
				template=EmailTemplate.RESET_PASSWORD_SUCCESS,
				recipients=[EmailRecipient(email=user.email, name=f"{user.first_name} {user.last_name}")],
				subject=f"Your {settings.APP_NAME} Password Has Been Reset",
				sender_email=settings.SENDER_EMAIL,
				sender_name=settings.SENDER_NAME,
				template_variables={
					"user_name": user.first_name,
					"app_name": settings.APP_NAME,
					"login_url": f"{settings.FRONTEND_URL}/login",
					"reset_time": datetime.utcnow().strftime("%B %d, %Y at %I:%M %p UTC"),
					"year": datetime.utcnow().year,
				},
			)
			self.email_service.send_email(email_message)
			logger.info(f"Password reset confirmation sent to {user.email}")
		except Exception as e:
			logger.error(f"Failed to send password reset confirmation: {str(e)}")

	async def register_user(self, payload: RegistrationRequest) -> User:
		"""
		Register a new user in the database with hashed password
		"""
		try:
			existing_user = self.db.query(User).filter(User.email == payload.email).first()

			if existing_user is not None:
				raise ApiException("User with the same email already exists")

			username = await self.generate_username()
			account = Account(
				type=AccountType.USER,
				status=AccountStatus.INACTIVE,
				handle=username,
			)

			self.db.add(account)
			self.db.flush(account)

			hashed_password = self.encryption_service.hash_password(payload.password)

			user = User(
				first_name=payload.first_name,
				gender=payload.gender,
				last_name=payload.last_name,
				email=str(payload.email).lower(),
				hashed_password=hashed_password,
				is_external=True,
				account_id=account.id,
			)
			user.account_id = account.id

			self.db.add(user)
			self.db.flush(user)
			self.db.commit()

			name = f"{payload.first_name} {payload.last_name}"
			await self.send_account_verification_email(user.id, name, user.email)

			return user
		except Exception as e:
			logger.error(f"Registration error: {str(e)}")
			self.db.rollback()
			raise ApiException("Failed to create an account")

	async def send_account_verification_email(self, user_id: str, name: str, email: str):
		verification = await self.generate_verification_code(
			email, VerificationType.ACCOUNT_REGISTRATION, user_id=user_id
		)

		self.email_service.send_email(
			EmailMessage(
				template=EmailTemplate.ACCOUNT_CONFIRMATION,
				recipients=[EmailRecipient(email=email, name=name)],
				subject=f"Welcome to {settings.APP_NAME}, please verify your email",
				sender_email=settings.SENDER_EMAIL,
				sender_name=settings.SENDER_NAME,
				template_variables={
					"user_name": name,
					"verification_url": f"{settings.FRONTEND_URL}/auth/verify-email?token={self._generate_email_verification_token(email)}",
					"code": verification.code,
				},
			)
		)

	def _generate_email_verification_token(self, email: str) -> str:
		"""Generate email verification token"""
		return self.encryption_service.generate_email_verification_token(email)

	def verify_email_verification_token(self, token: str) -> Optional[str]:
		"""Verify email verification token and return email if valid"""
		try:
			return self.encryption_service.verify_email_verification_token(token)
		except Exception:
			return None

	async def send_verification_email(self, user: User, verification_token: str):
		"""Send verification email to user"""

		verification_url = f"{settings.FRONTEND_URL}/verify-email?token={verification_token}"

		recipient = EmailRecipient(email=user.email, name=f"{user.first_name} {user.last_name}")

		template_variables = {
			"user_name": user.first_name,
			"verification_url": verification_url,
			"app_name": settings.APP_NAME,
			"year": datetime.utcnow().year,
		}

		try:
			result = await email_service.send_template_email(
				template=EmailTemplate.ACCOUNT_CONFIRMATION,
				recipients=[recipient],
				subject=f"Verify your {settings.APP_NAME} account",
				sender_email=settings.SENDER_EMAIL,
				sender_name=settings.APP_NAME,
				template_variables=template_variables,
			)

			if result.get("success"):
				logger.info(f"Verification email sent successfully to {user.email}")
			else:
				logger.error(f"Failed to send verification email to {user.email}: {result.get('error')}")

		except Exception as e:
			logger.error(f"Error sending verification email to {user.email}: {str(e)}")

	def verify_user_email(self, verification_data: EmailVerificationRequest) -> bool:
		"""Verify user's email address using verification token"""
		try:
			email = self.verify_email_verification_token(verification_data.token)
			if not email:
				raise ApiException("Invalid or expired verification token")

			user = self.db.query(User).join(Account, User.account).filter(User.email == email).first()
			if not user:
				raise ApiException("User not found")

			if user.account.status == AccountStatus.ACTIVE:
				logger.info(f"User {email} attempted to verify already active account")
				return {"success": True, "message": "Email already verified", "already_verified": True}

			# Activate the account
			user.account.status = AccountStatus.ACTIVE
			user.updated_at = datetime.utcnow()

			self.db.add(user.account)
			self.db.add(user)
			self.db.commit()

			logger.info(f"Email verified successfully for user: {email}")

			return {"success": True, "message": "Email verified successfully", "already_verified": False}

		except Exception as e:
			logger.error(f"Email verification error: {str(e)}")
			self.db.rollback()
			raise ApiException("Email verification failed")

	async def _send_welcome_email(self, user: User):
		"""Send welcome email after successful verification"""
		from src.core.services.email_service import send_welcome_email

		try:
			send_welcome_email(
				recipient_email=user.email,
				recipient_name=f"{user.first_name} {user.last_name}",
				sender_email=settings.FROM_EMAIL,
				user_name=user.first_name,
				app_name=settings.APP_NAME,
			)
		except Exception as e:
			logger.error(f"Failed to send welcome email to {user.email}: {str(e)}")

	def verify_email_link(self, token: str) -> bool:
		"""
		Verify user's email address via GET request (clicking verification link).

		Args:
			token: Email verification token from query parameter

		Returns:
			bool: True if verification successful, raises ApiException otherwise
		"""
		try:
			verification_data = EmailVerificationRequest(token=token)

			success = AuthService.verify_user_email(verification_data)

			if success:
				logger.info("Email verification successful via link click")
				return True
			else:
				raise ApiException("Verification failed - invalid or expired token")

		except ApiException as e:
			logger.error(f"Email verification error via link: {str(e)}")
			raise e
		except Exception as e:
			logger.error(f"Unexpected error during email verification: {str(e)}")
			raise ApiException("An unexpected error occurred during verification")

	async def resend_account_verification(self, request: Request, data: VerificationRetryRequest):
		"""Resend verification email to user"""
		try:
			user = self.db.query(User).filter(User.email == str(data.email)).first()

			if not user:
				raise ApiException(f"Verification resend requested for non-existent email: {data.email}")

			if user.verified:
				raise ApiException(f"Verification resend requested for already active account: {data.email}")

			auth_type = request.cookies.get(SESSION_TYPE_KEY_COOKIE)

			if auth_type != AuthType.VERIFICATION.value:
				raise ApiException("Could not resend verification email")

			name = f"{user.first_name} {user.last_name}"
			await self.send_account_verification_email(user.id, name, user.email)

			return True
		except Exception as e:
			logger.error(f"Resend verification error: {str(e)}")
			raise e

	def get_remember_me_sessions(self, user_id: str) -> list[SessionInfo]:
		"""Get only remember me sessions for a user"""
		sessions = (
			self.db.query(Session)
			.filter(
				Session.user_id == user_id,
				Session.remember_me,
				Session.is_active,
				Session.expires_at > datetime.utcnow(),
			)
			.order_by(Session.last_activity.desc())
			.all()
		)

		return [
			SessionInfo(
				session_id=session.session_token,
				ip_address=session.ip_address,
				user_agent=session.user_agent,
				device=session.device,
				created_at=session.created_at,
				last_activity=session.last_activity,
				is_current=False,
				remember_me=session.remember_me,
				expires_at=session.expires_at,
			)
			for session in sessions
		]

	def revoke_remember_me_sessions(self, user_id: str) -> int:
		"""Revoke all remember me sessions for a user"""
		try:
			current_time = datetime.utcnow()

			remember_me_sessions = (
				self.db.query(Session).filter(Session.user_id == user_id, Session.remember_me, Session.is_active).all()
			)

			for session in remember_me_sessions:
				session.is_active = False
				session.logged_out_at = current_time
				self.db.add(session)

			self.db.commit()

			count = len(remember_me_sessions)
			logger.info(f"Revoked {count} remember me sessions for user {user_id}")
			return count

		except Exception as e:
			logger.error(f"Error revoking remember me sessions: {str(e)}")
			self.db.rollback()
			return 0

	def is_remember_me_session(self, session_token: str) -> bool:
		"""Check if a session is a remember me session"""
		try:
			session = self.db.query(Session).filter(Session.session_token == session_token, Session.is_active).first()

			return session.remember_me if session else False

		except Exception:
			return False

	async def generate_verification_code(
		self, email: str, type_: VerificationType, user_id: str, expiry_minutes: int = 30
	) -> EmailVerification:
		"""Generate and save a verification code for the given email and type."""

		existing_verification = (
			self.db.query(EmailVerification)
			.filter(EmailVerification.email == email, EmailVerification.type == type_, ~EmailVerification.verified)
			.first()
		)

		code = "".join(random.choices(string.ascii_uppercase + string.digits, k=6))
		new_expires_at = datetime.now() + timedelta(minutes=expiry_minutes)

		if existing_verification:
			existing_verification.code = code
			existing_verification.expires_at = new_expires_at
			existing_verification.user_id = user_id
			verification = existing_verification
		else:
			verification = EmailVerification(
				email=email,
				code=code,
				type=type_,
				expires_at=new_expires_at,
				verified=False,
				user_id=user_id,
			)
			self.db.add(verification)

		self.db.commit()
		self.db.refresh(verification)
		return verification

	async def verify_code(self, email: str, code: str, type_: VerificationType) -> bool:
		"""Verify a code. Marks it as used if valid."""
		try:
			record = (
				self.db.query(EmailVerification)
				.filter(
					EmailVerification.email == email,
					EmailVerification.code == code,
					EmailVerification.type == type_,
					EmailVerification.expires_at > datetime.utcnow(),
					~EmailVerification.verified,
				)
				.order_by(EmailVerification.created_at.desc())
				.first()
			)

			if not record:
				return False

			record.verified = True
			self.db.commit()
			return True

		except Exception as e:
			self.db.rollback()
			raise e
