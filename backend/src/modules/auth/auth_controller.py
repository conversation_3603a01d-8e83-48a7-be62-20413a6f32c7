from fastapi import Depends, Header, HTTPException, Query, Request, status, Response
from fastapi.responses import JSONResponse

from src.core.dtos.auth_dtos import AuthDto
from src.core.exceptions.api import ApiException
from src.core.guards.auth_guard import auth_guard
from src.core.http.responses import bad_request, unauthorized
from src.core.shared_schema import BaseResponse
from src.core.utils.cookies import set_auth_cookies
from src.modules.auth.auth_schema import (
	EmailVerificationRequest,
	EmailVerificationResponse,
	LoginRequest,
	LoginResponse,
	LogoutRequest,
	LogoutResponse,
	PasswordReset,
	PasswordResetRequest,
	PasswordResetTokenResponse,
	PasswordResetTokenValidation,
	RefreshTokenRequest,
	RefreshTokenResponse,
	RegistrationRequest,
	RememberMeStatusRequest,
	RememberMeStatusResponse,
	UserResponse,
	UserSessionsResponse,
	VerificationRetryRequest,
)
from src.modules.auth.auth_service import AuthService

auth_service = AuthService()


async def register(user_data: RegistrationRequest) -> BaseResponse[UserResponse]:
	"""
	Register a new user.

	Args:
		user_data: User registration data

	Returns:
		UserResponse: The newly created user
	"""
	user = await auth_service.register_user(user_data)

	data = UserResponse(
		first_name=user.first_name,
		last_name=user.last_name,
		middle_name=user.middle_name,
		gender=user.gender,
		username=user.account.handle if hasattr(user, "account") else "",
		email=user.email,
		user_id=user.id,
		created_at=user.created_at,
		updated_at=user.updated_at,
	)

	return BaseResponse[UserResponse](data=data)


def verify_email(verification_data: EmailVerificationRequest) -> EmailVerificationResponse:
	"""
	Verify user's email address using verification token.

	Args:
		verification_data: Email verification request data (token)

	Returns:
		EmailVerificationResponse: Verification confirmation
	"""
	try:
		# Complete the email verification process
		result = auth_service.verify_user_email(verification_data)

		return EmailVerificationResponse(
			success=result["success"],
			message=result["message"],
		)

	except ApiException as e:
		return EmailVerificationResponse(success=False, message=e.message)

	except Exception as e:
		return EmailVerificationResponse(success=False, message=f"Email verification failed: {str(e)}")


def verify_email_link(token: str = Query(..., description="Email verification token")) -> EmailVerificationResponse:
	"""
	Verify user's email address via GET request (clicking verification link).

	Args:
		token: Email verification token from query parameter

	Returns:
		HTMLResponse: Success or error page
	"""
	try:
		# Create EmailVerificationRequest object
		verification_data = EmailVerificationRequest(token=token)

		# Complete the email verification process
		result = auth_service.verify_user_email(verification_data)

		if result["success"]:
			if result["already_verified"]:
				return JSONResponse(
					status_code=status.HTTP_200_OK,
					content={
						"success": True,
						"message": "Email already verified. You can now log in to your account.",
						"verified": True,
						"redirect_url": "http://localhost:3000/login",
					},
				)
			else:
				return JSONResponse(
					status_code=status.HTTP_200_OK,
					content={
						"success": True,
						"message": "Email verified successfully! You can now log in to your account.",
						"verified": True,
						"already_verified": False,
						"redirect_url": "http://localhost:3000/login",
					},
				)
		else:
			return JSONResponse(
				status_code=status.HTTP_400_BAD_REQUEST,
				content={"success": False, "message": result["message"], "verified": False, "already_verified": False},
			)

	except ApiException as e:
		return JSONResponse(
			status_code=status.HTTP_400_BAD_REQUEST,
			content={"success": False, "message": e.message, "verified": False, "already_verified": False},
		)

	except Exception:
		return JSONResponse(
			status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
			content={
				"success": False,
				"message": "An unexpected error occurred during verification",
				"verified": False,
				"already_verified": False,
			},
		)


@auth_guard.authenticated
async def resend_verification(request: Request, data: VerificationRetryRequest):
	"""
	Resend email verification link to user.

	Args:
	   resend_request: Request with user's email

	Returns:
	   JSONResponse: Confirmation message
	"""
	await auth_service.resend_account_verification(request, data)
	return BaseResponse[bool](data=True)


async def login(request: Request, form_data: LoginRequest, response: Response) -> BaseResponse[AuthDto]:
	"""
	Authenticate a user and return access tokens.

	Args:
		request: Request with login details
		form_data: Form with username and password
		response: Response with access token

	Returns:
		BaseResponse[AuthDto]: Auth data with auth tokens
	"""
	tokens, auth = await auth_service.authenticate_user(form_data, request)

	if not auth:
		raise ApiException("Invalid username or password")

	set_auth_cookies(response, tokens["access_token"], tokens["refresh_token"], auth.auth_type)

	return BaseResponse[AuthDto](data=auth)


def login_with_remember_me(request: Request, login_data: LoginRequest) -> BaseResponse[LoginResponse]:
	"""
	Authenticate a user with remember me option and return access tokens.

	Args:
		request: FastAPI request object
		login_data: Login data with username, password, and remember_me option

	Returns:
		BaseResponse[LoginResponse]: User data with auth tokens
	"""
	try:
		user = auth_service.authenticate_user(login_data.username, login_data.password, request, login_data.remember_me)
		if not user:
			raise bad_request("Invalid username or password")

		return BaseResponse(data=user)

	except ApiException as e:
		raise bad_request(e.message)


def logout(logout_request: LogoutRequest, authorization: str = Header(None, alias="Authorization")) -> LogoutResponse:
	"""
	Logout user by invalidating sessions.

	Args:
		logout_request: Logout configuration
		authorization: Authorization header with Bearer token

	Returns:
		LogoutResponse: Logout confirmation
	"""
	try:
		# Extract token from Authorization header
		if not authorization.startswith("Bearer "):
			raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authorization header format")

		token = authorization.split(" ")[1]

		if logout_request.revoke_remember_me:
			current_user = auth_service.get_current_user(token)
			if current_user:
				remember_me_revoked = auth_service.revoke_remember_me_sessions(current_user.id)
			else:
				remember_me_revoked = 0
		else:
			remember_me_revoked = 0

		# Logout user
		success, sessions_count = auth_service.logout_user(
			token, logout_request.all_sessions, logout_request.session_id
		)

		if success:
			message = f"Successfully logged out from {sessions_count} session(s)"
			if logout_request.all_sessions:
				message = f"Successfully logged out from all {sessions_count} sessions"
			if logout_request.revoke_remember_me:
				message += f" and revoked remember me sessions: {remember_me_revoked}"

			return LogoutResponse(
				success=True,
				message=message,
				sessions_logged_out=sessions_count,
				remember_me_sessions_revoked=remember_me_revoked,
			)
		else:
			return LogoutResponse(
				success=False,
				message="Logout failed - no active session found",
				sessions_logged_out=0,
				remember_me_sessions_revoked=remember_me_revoked,
			)

	except ApiException as e:
		return bad_request(e.message)


def get_user_sessions(authorization: str) -> UserSessionsResponse:
	"""
	Get all active sessions for the current user.

	Args:
		authorization: Authorization header with Bearer token

	Returns:
		UserSessionsResponse: List of active sessions
	"""
	try:
		# Extract token from Authorization header
		if not authorization.startswith("Bearer "):
			raise unauthorized("Invalid authorization header format")

		token = authorization.split(" ")[1]

		# Get current user
		current_user = auth_service.get_current_user(token)
		if not current_user:
			raise unauthorized("Could not validate credentials")

		# Get user sessions
		sessions = auth_service.get_user_sessions(current_user.id)

		remember_me_count = sum(1 for s in sessions if s.remember_me)
		regular_count = len(sessions) - remember_me_count

		return UserSessionsResponse(
			sessions=sessions,
			total_sessions=len(sessions),
			remember_me_sessions=remember_me_count,
			regular_sessions=regular_count,
		)

	except ApiException as e:
		raise unauthorized(e.message)


def refresh_token(refresh_request: RefreshTokenRequest) -> RefreshTokenResponse:
	"""
	Enhanced refresh token with remember me support
	"""
	try:
		new_tokens = auth_service.refresh_access_token(refresh_request.refresh_token)

		return RefreshTokenResponse(
			access_token=new_tokens["access_token"],
			token_type=new_tokens["token_type"],
			expires_in=3600,  # 1 hour default
			remember_me=new_tokens.get("remember_me", False),
		)

	except ApiException as e:
		raise bad_request(e.message)


def get_current_user(token: str = Depends(auth_service.oauth2_scheme)) -> UserResponse:
	"""
	Get the current authenticated user.

	Args:
		token: JWT from Authorization header

	Returns:
		UserResponse: Current user data
	"""
	try:
		# Get current user from service
		current_user = auth_service.get_current_user(token)
		if not current_user:
			raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not validate credentials")

		# Get full user details from database
		user = auth_service.get_user_by_id(current_user.id)
		if not user:
			raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

		# Convert to UserResponse
		account_status = (
			"ACTIVE"
			if hasattr(user, "account") and user.account and user.account.status.value == "ACTIVE"
			else (user.account.status.value if hasattr(user, "account") and user.account else "UNKNOWN")
		)

		return UserResponse(
			first_name=user.first_name,
			last_name=user.last_name,
			middle_name=user.middle_name,
			username=user.account.handle if hasattr(user, "account") and user.account else current_user.username,
			email=user.email,
			user_id=user.id,
			status=account_status,
			created_at=user.created_at,
			updated_at=user.updated_at,
		)

	except HTTPException:
		# Re-raise any HTTPExceptions from the service
		raise
	except Exception as e:
		# Handle any other exceptions
		raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=f"Authentication failed: {str(e)}")


def request_password_reset(
	reset_request: PasswordResetRequest,
) -> JSONResponse:
	"""
	Request a password reset email.

	Args:
		reset_request: Request with user's email

	Returns:
		JSONResponse: Confirmation message
	"""
	try:
		auth_service.initiate_password_reset(reset_request.email)

		return JSONResponse(
			status_code=status.HTTP_200_OK,
			content={
				"success": True,
				"message": "If an account with this email exists, password reset instructions have been sent.",
				"email": reset_request.email,
			},
		)
	except Exception as e:
		print(f"Password reset error: {str(e)}")

		return JSONResponse(
			status_code=status.HTTP_200_OK,
			content={
				"success": True,
				"message": "If an account with this email exists, password reset instructions have been sent.",
				"email": reset_request.email,
			},
		)


def reset_password_link(token: str = Query(..., description="Password reset token")) -> JSONResponse:
	"""
	Display password reset page when user clicks email link.

	Args:
		token: Password reset token from query parameter

	Returns:
		JSONResponse: Password reset page with token
	"""
	try:
		is_valid, email = auth_service.validate_password_reset_token(token)

		if not is_valid:
			return JSONResponse(
				status_code=status.HTTP_400_BAD_REQUEST,
				content={"success": False, "message": "Invalid or expired password reset token."},
			)

		return JSONResponse(
			status_code=status.HTTP_200_OK,
			content={"success": True, "message": "Password reset token is valid.", "token": token, "email": email},
		)

	except Exception as e:
		return JSONResponse(
			status_code=status.HTTP_400_BAD_REQUEST,
			content={"success": False, "message": f"Error validating password reset token: {str(e)}"},
		)


def get_remember_me_status(
	request: RememberMeStatusRequest, authorization: str = Header(None, alias="Authorization")
) -> RememberMeStatusResponse:
	"""
	Check if current session is a remember me session
	"""
	try:
		if not authorization or not authorization.startswith("Bearer "):
			raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authorization header format")

		token = authorization.split(" ")[1]
		current_user = auth_service.get_current_user(token)

		if not current_user:
			raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not validate credentials")

		# Check if session is remember me
		if request.session_id:
			is_remember_me = auth_service.is_remember_me_session(request.session_id)
		else:
			# Check current session from token
			sessions = auth_service.get_user_sessions(current_user.id)
			current_session = next((s for s in sessions if s.is_current), None)
			is_remember_me = current_session.remember_me if current_session else False

		return RememberMeStatusResponse(
			is_remember_me=is_remember_me, expires_at=current_session.expires_at if current_session else None
		)
	except HTTPException:
		# Re-raise any HTTPExceptions from the service
		raise
	except Exception as e:
		# Handle any other exceptions
		raise HTTPException(
			status_code=status.HTTP_400_BAD_REQUEST, detail=f"Failed to check remember me status: {str(e)}"
		)


def validate_password_reset_token(token_validation: PasswordResetTokenValidation) -> PasswordResetTokenResponse:
	"""
	Validate a password reset token.

	Args:
		token_validation: Token validation request

	Returns:
		PasswordResetTokenResponse: Token validation result
	"""
	try:
		is_valid, email = auth_service.validate_password_reset_token(token_validation.token)

		if is_valid:
			return PasswordResetTokenResponse(valid=True, email=email)
		else:
			return PasswordResetTokenResponse(valid=False, email=None)

	except Exception:
		return PasswordResetTokenResponse(valid=False, email=None)


def reset_password(reset_data: PasswordReset) -> JSONResponse:
	"""
	Complete the password reset process.

	Args:
		reset_data: Reset token and new password

	Returns:
		JSONResponse: Confirmation message
	"""
	try:
		# Complete the password reset
		success = auth_service.complete_password_reset(reset_data.token, reset_data.new_password)

		if success:
			# Return a success message
			return JSONResponse(
				status_code=status.HTTP_200_OK,
				content={
					"success": True,
					"message": "Password has been reset successfully. You can now log in with your new password.",
				},
			)
		else:
			# Return an error if the reset failed
			return JSONResponse(
				status_code=status.HTTP_400_BAD_REQUEST,
				content={"success": False, "message": "Password reset failed. The token may be invalid or expired."},
			)

	except HTTPException:
		# Re-raise any HTTPExceptions from the service
		raise
	except Exception:
		# Handle any other exceptions
		raise HTTPException(
			status_code=status.HTTP_400_BAD_REQUEST,
			content={
				"success": False,
				"message": "Password reset failed. Please try again or request a new reset link.",
			},
		)
