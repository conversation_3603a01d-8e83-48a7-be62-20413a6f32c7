from fastapi import APIRouter, status, Response, Request
from fastapi.responses import HTMLResponse
from fastapi.security import OAuth2<PERSON><PERSON>wordBearer

import src.modules.auth.auth_controller as controller
import src.modules.auth.auth_schema as schema
from src.core.dtos.auth_dtos import AuthDto
from src.core.shared_schema import BaseResponse
from src.modules.auth.auth_schema import LoginRequest

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="login")

router = APIRouter(tags=["auth"])

router.add_api_route(
	path="/register",
	endpoint=controller.register,
	methods=["POST"],
	response_model=BaseResponse[schema.UserResponse],
	status_code=status.HTTP_201_CREATED,
	summary="Register a new user",
	description="Register a new user account. Account will be inactive until email is verified.",
)


@router.post(
	"/login",
	response_model=BaseResponse[AuthDto],
	summary="Login to get access token",
	description="Authenticate with email and password to receive JWT tokens",
)
async def login(request: Request, form: LoginRequest, response: Response):
	data = await controller.login(request, form, response)
	return data


router.add_api_route(
	path="/logout",
	endpoint=controller.logout,
	methods=["POST"],
	response_model=schema.LogoutResponse,
	summary="Logout user",
	description="Logout user from current session or all sessions",
)

router.add_api_route(
	path="/sessions",
	endpoint=controller.get_user_sessions,
	methods=["GET"],
	response_model=schema.UserSessionsResponse,
	summary="Get user sessions",
	description="Get all active sessions for the current user",
)

router.add_api_route(
	path="/refresh",
	endpoint=controller.refresh_token,
	methods=["POST"],
	response_class=HTMLResponse,
	summary="Refresh access token",
	description="Get a new access token using refresh token",
)

router.add_api_route(
	path="/me",
	endpoint=controller.get_current_user,
	methods=["GET"],
	response_model=schema.UserResponse,
	summary="Get current user",
	description="Get the current authenticated user's information",
)

# Email verification routes
router.add_api_route(
	path="/verify-email",
	endpoint=controller.verify_email,
	methods=["POST"],
	response_model=schema.EmailVerificationResponse,
	summary="Verify email address",
	description="Verify user email address with verification token",
)

router.add_api_route(
	path="/verify-email-link",
	endpoint=controller.verify_email_link,
	methods=["GET"],
	response_class=HTMLResponse,
	summary="Verify email via link",
	description="Verify user email address by clicking verification link from email",
)

router.add_api_route(
	path="/resend-account-verification",
	endpoint=controller.resend_verification,
	methods=["POST"],
	summary="Resend verification code email",
	description="Resend email verification link and code to user",
)

router.add_api_route(
	path="/request-password-reset",
	endpoint=controller.request_password_reset,
	methods=["POST"],
	response_model=BaseResponse[bool],
	summary="Request password reset",
	description="Request a password reset email",
)

router.add_api_route(
	path="/validate-reset-token",
	endpoint=controller.validate_password_reset_token,
	methods=["POST"],
	response_model=schema.PasswordResetTokenResponse,
	summary="Validate password reset token",
	description="Validate password reset token and return email if valid",
)

router.add_api_route(
	path="/reset-password",
	endpoint=controller.reset_password_link,
	methods=["GET"],
	response_class=HTMLResponse,
	summary="Password reset page",
	description="Display password reset page when user clicks email link",
)

router.add_api_route(
	path="/reset-password",
	endpoint=controller.reset_password,
	methods=["POST"],
	summary="Reset user password",
	description="Reset user password using reset token",
)
