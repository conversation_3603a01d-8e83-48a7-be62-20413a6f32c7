from fastapi import APIRouter, status
from fastapi_pagination import Page
from src.modules.users.users_schema import UserResponse, UserDepartmentsResponse, UserRolesResponse
from src.modules.users import users_controller as controller


router = APIRouter(tags=["users"])

router.add_api_route(
	path="",
	endpoint=controller.index,
	name="Get and Search For Users",
	methods=["GET"],
	status_code=status.HTTP_200_OK,
	response_model=Page[UserResponse],
)

router.add_api_route(
	path="",
	name="Create User",
	endpoint=controller.create,
	methods=["POST"],
	response_model=UserResponse,
	status_code=status.HTTP_201_CREATED,
)

router.add_api_route(
	name="Edit User Attributes",
	path="/{id}",
	endpoint=controller.update,
	methods=["PUT"],
	response_model=UserResponse,
	status_code=status.HTTP_200_OK,
)

router.add_api_route(
	path="/{id}",
	endpoint=controller.delete,
	methods=["DELETE"],
	name="Delete A User",
	status_code=status.HTTP_204_NO_CONTENT,
)

# User-Department Assignment Routes
router.add_api_route(
	path="/assign-departments",
	endpoint=controller.assign_user_to_departments,
	methods=["POST"],
	name="Assign User to Departments",
	response_model=UserDepartmentsResponse,
	status_code=status.HTTP_201_CREATED,
)

router.add_api_route(
	path="/{user_id}/unassign-departments",
	endpoint=controller.unassign_user_from_departments,
	methods=["DELETE"],
	name="Unassign User from Departments",
	status_code=status.HTTP_204_NO_CONTENT,
)

# User-Role Assignment Routes
router.add_api_route(
	path="/assign-roles",
	endpoint=controller.assign_roles_to_user,
	methods=["POST"],
	name="Assign Roles to User",
	response_model=UserRolesResponse,
	status_code=status.HTTP_201_CREATED,
)

router.add_api_route(
	path="/{user_id}/unassign-roles",
	endpoint=controller.unassign_roles_from_user,
	methods=["DELETE"],
	name="Unassign Roles from User",
	status_code=status.HTTP_204_NO_CONTENT,
)
