from typing import Optional, List
from uuid import UUID
from pydantic import BaseModel, ConfigDict

from src.core.shared_schema import BaseRequest


class RoleBaseDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)

	code: str
	name: str
	description: Optional[str] = None


class RoleCreate(RoleBaseDto):
	"""Schema for creating a new role"""

	pass


class RoleUpdate(BaseModel):
	"""Schema for updating a role"""

	model_config = ConfigDict(from_attributes=True)

	code: Optional[str] = None
	name: Optional[str] = None
	description: Optional[str] = None


class PermissionDto(BaseModel):
	"""Schema for permission data in role response"""

	model_config = ConfigDict(from_attributes=True)

	id: UUID
	code: str
	display_value: str
	description: Optional[str] = None


class RoleResponse(RoleBaseDto):
	"""Schema for role response"""

	id: UUID
	role_permissions: List[PermissionDto] = []


class RoleFilters(BaseRequest):
	"""Schema for filtering roles"""

	code: Optional[str] = None
	name: Optional[str] = None
	description: Optional[str] = None


# Role-Permission Assignment Schemas
class RolePermissionAssignRequest(BaseModel):
	"""Schema for assigning permissions to a role"""

	model_config = ConfigDict(from_attributes=True)

	role_id: UUID
	permission_ids: List[UUID]
