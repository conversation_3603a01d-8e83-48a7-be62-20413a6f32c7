from fastapi import APIRouter

from src.core.dtos.financial_dtos import InvoiceDto
from src.core.shared_schema import Pagination
from src.modules.financial import financial_controller as controller

financial_router_v1 = APIRouter(tags=["financial"])

financial_router_v1.add_api_route(
	path="/invoices",
	methods=["GET"],
	endpoint=controller.fetch_invoices_handler,
	response_model=Pagination[InvoiceDto],
)
