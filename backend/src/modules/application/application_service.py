import random

from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from pydantic.types import UUID4
from sqlalchemy import and_
from sqlalchemy.orm import Session as OrmSession

from src.config.db.models.application import Application, ApplicationStatus, ApplicationType
from src.config.db.models.application_document import ApplicationDocument
from src.core.base.base_repository import BaseRepository
from src.core.dtos.application_dtos import ApplicationDto, to_application_dto
from src.core.exceptions.api import ApiException
from src.core.logger.internal_logger import get_logger
from src.core.shared_schema import Pagination, VoidRequest
from src.modules.application.application_schema import ApplicationFilter
from src.modules.financial.fee_service import FeeService


class ApplicationService(BaseRepository):
	def __init__(self, db: OrmSession = None):
		if db is not None:
			self.db = db
		else:
			super().__init__()

		self.fee_service = FeeService(self.db)
		self.logger = get_logger(__name__)

	async def _generate_unique_application_code(self):
		"""
		Generate a unique 8-character application code without '0' and 'O'.

		Returns:
			str: Unique 8-character application code
		"""
		allowed_chars = "123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"

		max_attempts = 200

		for _ in range(max_attempts):
			code = "".join(random.choices(allowed_chars, k=8))

			existing_application = self.db.query(Application).filter(Application.code == code).first()

			if existing_application is None:
				return code

		raise ApiException(f"Unable to generate unique application code after {max_attempts} attempts")

	async def create_organization_registration(self, org_id: UUID4, income=0.0) -> Application:
		try:
			existing_application = (
				self.db.query(Application)
				.filter(
					and_(
						Application.organization_id == org_id,
						Application.type == ApplicationType.ORGANIZATION_REGISTRATION,
					)
				)
				.first()
			)

			if existing_application is not None:
				raise ApiException("Organization already has the application")

			application = Application(
				organization_id=org_id,
				status=ApplicationStatus.DRAFT,
				type=ApplicationType.ORGANIZATION_REGISTRATION,
				code=self._generate_unique_application_code(),
				created_by=self.current_user.id,
			)
			self.db.add(application)
			self.db.flush()

			await self.fee_service.create_application_fee(application, income)

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return application
		except Exception as e:
			self.logger.error(f"Failed to create organization application: {str(e)}")
			raise e

	async def retrieve_applications(self, filter: ApplicationFilter) -> Pagination[ApplicationDto]:
		try:
			query = self.db.query(Application)

			if filter.organization_id:
				query = query.filter(Application.organization_id == filter.organization_id)

			if filter.status:
				query = query.filter(Application.status == filter.status)

			if filter.type:
				query = query.filter(Application.type == filter.type)

			if filter.start_date:
				query = query.filter(Application.created_at >= filter.start_date)

			if filter.end_date:
				query = query.filter(Application.created_at <= filter.end_date)

			paginated_result = paginate(query, Params(page=filter.page, size=filter.size))
			data = [to_application_dto(row, filter.segments) for row in paginated_result.items]

			return Pagination.from_query_result(data, paginated_result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve applications: {str(e)}")
			raise

	async def _verify_application_payment(self, application: Application):
		try:
			payment_document = self.db.query(ApplicationDocument).first()

			return payment_document

		except Exception as e:
			self.logger.error(f"Payment verification failed: {str(e)}")
			raise

	async def submit_application_for_review(self, application_id: UUID4):
		try:
			application = self.db.query(Application).filter(Application.id == application_id).first()

			if application is None:
				raise ApiException("Application not found")

			if application.status != ApplicationStatus.DRAFT.value:
				raise ApiException(f"Current application is in {application.status} phase")

			invoice = await self._verify_application_payment(application)
			print(invoice)
		except Exception as e:
			self.logger.error(f"Failed to start application review: {str(e)}")
			raise

	async def reject_application(self, application_id: UUID4):
		try:
			application = self.db.query(Application).filter(Application.id == application_id).first()

			if application is None:
				raise ApiException("Application not found")

			if application.status != ApplicationStatus.REVIEW.value:
				raise ApiException(f"Current application is in {application.status} phase")

			application.status = ApplicationStatus.REJECTED
			application.updated_by = self.current_user.id
			self.db.commit()

			#! TODO: Send rejection notification to applicant and email

			return to_application_dto(application)

		except Exception as e:
			self.logger.error(f"Failed to reject application: {str(e)}")
			raise

	async def delete_application(self, application_id: str, payload: VoidRequest):
		try:
			application = self.db.query(Application).filter(Application.id == application_id).first()

			if application is None:
				raise ApiException("Application not found")

			application.status = ApplicationStatus.SUSPENDED
			application.voided = True
			application.voided_by = self.current_user.id
			application.voided_reason = payload.reason
			self.db.commit()

		except Exception as e:
			self.logger.error(f"Failed to delete application: {str(e)}")
			raise e


application_service = ApplicationService()
