from fastapi import APIRouter, status

from src.core.dtos.application_dtos import ApplicationDto
from src.core.shared_schema import Pagination
from src.modules.application import application_controller as controller

application_router_v1 = APIRouter(tags=["application"])

application_router_v1.add_api_route(
	path="",
	endpoint=controller.fetch_applications_handler,
	methods=["GET"],
	response_model=Pagination[ApplicationDto],
	status_code=status.HTTP_200_OK,
)

application_router_v1.add_api_route(
	path="",
	endpoint=controller.delete_application_handler,
	methods=["PATCH"],
	status_code=status.HTTP_204_NO_CONTENT,
)

application_router_v1.add_api_route(
	path="/{application_id}/submit",
	endpoint=controller.create_application_handler,
	methods=["POST"],
	response_model=ApplicationDto,
	status_code=status.HTTP_200_OK,
)
