from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from pydantic import UUID4

from src.config.db.models import Country, LoadableItem
from src.config.db.models.district import District
from src.config.db.models.region import Region
from src.core.base.base_repository import BaseRepository
from src.core.exceptions.api import ApiException
from src.core.shared_schema import CurrentUser, Pagination
from src.core.utils import serializer

from .settings_schema import (
	CountryDto,
	CountryFilter,
	DistrictDto,
	DistrictFilter,
	LoadableItemDto,
	LoadableItemFilter,
	LoadableItemRequest,
	RegionDto,
	RegionFilter,
)


class SettingsService(BaseRepository):
	def __init__(self):
		super().__init__()

	def retrieve_countries(self, filter: CountryFilter) -> Pagination[CountryDto]:
		try:
			"""Retrieve countries based on the provided filter."""
			query = self.db.query(Country).order_by(Country.name.asc())

			if filter.name:
				query = query.filter(Country.name.ilike(f"%{filter.name}%"))

			paginated_result = paginate(query, Params(page=filter.page, size=filter.size))
			countries = [serializer.to_country_dto(row) for row in paginated_result.items]

			return Pagination.from_query_result(countries, paginated_result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve countries: {str(e)}")
			raise ApiException("Failed to retrieve countries")

	def retrieve_regions(self, filter: RegionFilter) -> Pagination[RegionDto]:
		try:
			"""Retrieve regions based on the provided filter."""
			query = self.db.query(Region)

			if filter.name:
				query = query.filter(Region.name.ilike(f"%{filter.name}%"))

			paginated_result = paginate(query, Params(page=filter.page, size=filter.size))

			regions = [serializer.to_region_dto(row) for row in paginated_result.items]
			return Pagination.from_query_result(regions, paginated_result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve regions: {str(e)}")
			raise ApiException("Failed to retrieve regions")

	def retrieve_districts(self, filter: DistrictFilter) -> Pagination[DistrictDto]:
		try:
			"""Retrieve districts based on the provided filter."""
			query = self.db.query(District).join(Region, District.region)

			if filter.name:
				query = query.filter(District.name.ilike(f"%{filter.name}%"))

			if filter.region_id:
				query = query.filter(District.region_id == filter.region_id)

			paginated_result = paginate(query, Params(page=filter.page, size=filter.size))
			data = [serializer.to_district_dto(row) for row in paginated_result.items]

			return Pagination.from_query_result(data, paginated_result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve districts: {str(e)}")
			raise ApiException("Failed to retrieve districts")

	def retrieve_loadable_items(self, filter: LoadableItemFilter) -> Pagination[LoadableItemDto]:
		try:
			"""Retrieve loadable items based on the provided filter."""
			query = self.db.query(LoadableItem)

			if filter.type:
				query = query.filter(LoadableItem.type == filter.type)

			if filter.display_value:
				query = query.filter(LoadableItem.display_value.ilike(f"%{filter.display_value}%"))

			if filter.code:
				query = query.filter(LoadableItem.code.ilike(f"%{filter.code}%"))

			paginated_result = paginate(query, Params(page=filter.page, size=filter.size))
			items = [serializer.to_loadable_item_dto(row) for row in paginated_result.items]

			return Pagination.from_query_result(items, paginated_result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve loadable items: {str(e)}")
			raise ApiException("Failed to retrieve loadable items")

	def add_loadable_item(self, user: CurrentUser, data: LoadableItemRequest) -> LoadableItemDto:
		"""Add a new loadable item."""
		try:
			item = self.db.query(LoadableItem).filter(LoadableItem.code == data.code).first()
			if item is not None:
				raise ApiException(f"Loadable item with code {data.code} already exists")

			item = (
				self.db.query(LoadableItem)
				.filter((LoadableItem.type == data.type) & (LoadableItem.display_value == data.display_value))
				.first()
			)
			if item is not None:
				raise ApiException("Loadable item with same name and type already exists")

			loadable_item = LoadableItem(
				type=data.type.upper(),
				code=data.code.upper(),
				display_value=data.display_value,
				description=data.description,
				created_by=user.id,
			)
			self.db.add(loadable_item)
			self.db.commit()
			self.db.refresh(loadable_item)

			return serializer.to_loadable_item_dto(loadable_item)
		except Exception as e:
			self.db.rollback()
			if isinstance(e, ApiException):
				raise
			raise ApiException(f"Failed to add loadable item: {str(e)}")

	def update_loadable_item(self, user: CurrentUser, data: LoadableItemRequest) -> LoadableItemDto:
		"""Update an existing loadable item."""
		try:
			loadable_item = self.db.query(LoadableItem).filter(LoadableItem.id == data.loadable_item_id).first()

			if loadable_item is None:
				raise ApiException("Could not find a loadable item to update")

			loadable_item.code = data.code.upper()
			loadable_item.type = data.type.upper()
			loadable_item.description = data.description
			loadable_item.display_value = data.display_value
			loadable_item.updated_by = user.user_id

			self.db.commit()
			self.db.refresh(loadable_item)

			return serializer.to_loadable_item_dto(loadable_item)
		except Exception as e:
			self.db.rollback()
			if isinstance(e, ApiException):
				raise
			raise ApiException(f"Failed to update loadable item: {str(e)}")

	def delete_loadable_item(self, user: CurrentUser, loadable_item_id: UUID4) -> bool:
		"""Delete (void) a loadable item."""
		try:
			loadable_item = self.db.query(LoadableItem).filter(LoadableItem.id == loadable_item_id).first()

			if loadable_item is None:
				raise ApiException("Could not find a loadable item to delete")

			loadable_item.voided_by = user.user_id
			loadable_item.voided = True

			self.db.commit()
			self.db.refresh(loadable_item)
			return True
		except Exception as e:
			self.db.rollback()
			if isinstance(e, ApiException):
				raise
			raise ApiException("Failed to delete loadable item")

	def get_loadable_item_by_code(self, code: str) -> LoadableItem | None:
		return self.db.query(LoadableItem).filter(LoadableItem.code == code).first()

	def get_loadable_item_by_id(self, id: UUID4) -> LoadableItem | None:
		return self.db.query(LoadableItem).filter(LoadableItem.id == id).first()

	def get_district_by_id(self, id: UUID4) -> District | None:
		return self.db.query(District).filter(District.id == id).first()


settings_service = SettingsService()
