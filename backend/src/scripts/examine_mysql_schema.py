#!/usr/bin/env python3
"""
MySQL Schema Examination Script

This script connects to your MySQL database and examines the schema
to help customize the migration script.

Usage:
    python examine_mysql_schema.py --mysql-host localhost --mysql-user root --mysql-password password --mysql-db old_db
"""

import argparse
import mysql.connector
from typing import Dict, List


def examine_mysql_schema(mysql_config: Dict):
    """Examine MySQL database schema"""
    try:
        conn = mysql.connector.connect(**mysql_config)
        cursor = conn.cursor(dictionary=True)

        print(f"Connected to MySQL database: {mysql_config['database']}")
        print("=" * 60)

        # Get all tables
        cursor.execute("SHOW TABLES")
        tables = [row[f"Tables_in_{mysql_config['database']}"] for row in cursor.fetchall()]

        print(f"Found {len(tables)} tables:")
        for table in tables:
            print(f"  - {table}")

        print("\n" + "=" * 60)

        # Examine key tables
        key_tables = ['users', 'roles', 'departments', 'user_roles', 'user_departments']

        for table in key_tables:
            if table in tables:
                print(f"\nTable: {table}")
                print("-" * 40)

                # Get table structure
                cursor.execute(f"DESCRIBE {table}")
                columns = cursor.fetchall()

                print("Columns:")
                for col in columns:
                    nullable = "NULL" if col['Null'] == 'YES' else "NOT NULL"
                    default = f"DEFAULT {col['Default']}" if col['Default'] else ""
                    extra = col['Extra'] if col['Extra'] else ""
                    print(f"  {col['Field']:<20} {col['Type']:<20} {nullable:<10} {default:<15} {extra}")

                # Get sample data count
                cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                count = cursor.fetchone()['count']
                print(f"Records: {count}")

                # Show sample data (first 3 records)
                if count > 0:
                    cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                    samples = cursor.fetchall()
                    print("Sample data:")
                    for i, sample in enumerate(samples, 1):
                        print(f"  Record {i}: {dict(sample)}")
            else:
                print(f"\nTable '{table}' not found in database")

        cursor.close()
        conn.close()

    except Exception as e:
        print(f"Error examining MySQL schema: {e}")


def main():
    parser = argparse.ArgumentParser(description='Examine MySQL database schema')
    parser.add_argument('--mysql-host', required=True, help='MySQL host')
    parser.add_argument('--mysql-user', required=True, help='MySQL username')
    parser.add_argument('--mysql-password', required=True, help='MySQL password')
    parser.add_argument('--mysql-db', required=True, help='MySQL database name')
    parser.add_argument('--mysql-port', type=int, default=3306, help='MySQL port')

    args = parser.parse_args()

    mysql_config = {
        'host': args.mysql_host,
        'user': args.mysql_user,
        'password': args.mysql_password,
        'database': args.mysql_db,
        'port': args.mysql_port,
        'charset': 'utf8mb4'
    }

    examine_mysql_schema(mysql_config)


if __name__ == '__main__':
    main()