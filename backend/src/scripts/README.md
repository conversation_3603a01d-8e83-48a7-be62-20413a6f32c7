# MySQL to PostgreSQL Migration Script

This script migrates data from your existing MySQL database to the new PostgreSQL schema for the NGORA system.

## Features

- **Minimalistic & Abstracted**: Clean, maintainable code with clear separation of concerns
- **SQLAlchemy Integration**: Uses the application's ORM models for data creation and validation
- **Pydantic Models**: Leverages application schemas for consistent data handling
- **EncryptionService**: Uses the application's encryption service for secure password hashing
- **UUID Mapping**: Converts MySQL integer IDs to PostgreSQL UUIDs with deterministic generation
- **Relationship Handling**: Properly migrates many-to-many relationships (user-roles, user-departments)
- **Error Handling**: Comprehensive logging and transaction rollback on failures
- **Audit Fields**: Maps MySQL audit fields to PostgreSQL audit fields (created_at, updated_at, voided, etc.)

## Prerequisites

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Ensure your PostgreSQL database is set up and accessible
3. Have your MySQL database credentials ready

## Usage

**Important**: Run the script from the backend directory to ensure proper module imports.

```bash
# Navigate to the backend directory
cd backend

# Run the migration script
python -m src.scripts.mysql_to_postgres_migration \
  --mysql-host localhost \
  --mysql-user root \
  --mysql-password your_password \
  --mysql-db your_mysql_database \
  --postgres-host localhost \
  --postgres-user postgres \
  --postgres-password your_pg_password \
  --postgres-db your_postgres_database
```

**Example with environment variables:**
```bash
# Set PostgreSQL password in environment
export POSTGRES_PASSWORD=your_pg_password

python -m src.scripts.mysql_to_postgres_migration \
  --mysql-host localhost \
  --mysql-user root \
  --mysql-password mysql_password \
  --mysql-db ngora_legacy \
  --postgres-db ngora_new
```

## Migration Order

The script migrates tables in the correct order to handle foreign key constraints:

1. **accounts** - Base account records
2. **users** - User records linked to accounts
3. **roles** - Role definitions
4. **departments** - Department definitions
5. **user_roles** - User-role assignments
6. **user_departments** - User-department assignments

## Schema Mapping

### MySQL → PostgreSQL Field Mappings

#### Users Table
| MySQL Field | PostgreSQL Field | Notes |
|-------------|------------------|-------|
| `id` | `id` | Converted to UUID |
| `username` | `handle` (in accounts) | Truncated to 15 chars |
| `first_name` | `first_name` | Direct mapping |
| `last_name` | `last_name` | Direct mapping |
| `email` | `email` | Direct mapping |
| `password` | `hashed_password` | Assumes already hashed |
| `phone` | `phone` | Direct mapping |
| `gender` | `gender` | Mapped to MALE/FEMALE enum |
| `email_verified_at` | `verified` | Converted to boolean |
| `status` | `status` (in accounts) | Mapped to ACTIVE/INACTIVE |
| `created_at` | `created_at` | Direct mapping |
| `updated_at` | `updated_at` | Direct mapping |

#### Additional PostgreSQL Fields
- `middle_name` - Set to NULL (add to MySQL if needed)
- `account_id` - Generated UUID linking to accounts table
- `is_external` - Default TRUE
- `voided` - Default FALSE
- `created_by`, `updated_by`, `voided_by` - Set to NULL initially

## Suggested Additional Tables/Columns

Based on the PostgreSQL schema, you may want to add these to your migration:

### 1. Sessions Table
```sql
-- Add to migration if you have user sessions in MySQL
CREATE TABLE sessions (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    session_token VARCHAR(255) UNIQUE,
    device VARCHAR(50),
    ip_address VARCHAR(20),
    expires_at TIMESTAMP WITH TIME ZONE,
    -- ... other session fields
);
```

### 2. Organizations & Members
```sql
-- If you have organizations in MySQL
CREATE TABLE organizations (
    id UUID PRIMARY KEY,
    name VARCHAR(255),
    account_id UUID REFERENCES accounts(id),
    -- ... other org fields
);

CREATE TABLE members (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    organization_id UUID REFERENCES organizations(id),
    role VARCHAR(50), -- OWNER, MEMBER
    is_active BOOLEAN DEFAULT TRUE
);
```

### 3. Permissions & Role Permissions
```sql
-- If you have permissions system
CREATE TABLE loadable_items (
    id UUID PRIMARY KEY,
    code VARCHAR(255),
    display_value VARCHAR(255),
    type VARCHAR(50) -- 'PERMISSION'
);

CREATE TABLE role_permissions (
    id UUID PRIMARY KEY,
    role_id UUID REFERENCES roles(id),
    permission_id UUID REFERENCES loadable_items(id)
);
```

### 4. Additional User Fields
Consider adding these fields to your MySQL users table before migration:
- `middle_name` VARCHAR(50)
- `username` VARCHAR(15) (for account handle)
- `is_external` BOOLEAN DEFAULT TRUE
- `verified` BOOLEAN DEFAULT FALSE

## Customization

### Adapting to Your MySQL Schema

1. **Update table/column names** in the SQL queries to match your MySQL schema
2. **Add missing fields** by modifying the migration methods
3. **Handle custom enums** by updating the mapping logic
4. **Add new tables** by creating additional migration methods

### Example: Adding Middle Name Support
```python
def migrate_users(self):
    mysql_query = """
    SELECT id, first_name, middle_name, last_name, email, password, phone, gender,
           created_at, updated_at, email_verified_at
    FROM users
    WHERE deleted_at IS NULL
    """

    # Update the params tuple to include middle_name
    params = (
        user_id,
        user['first_name'],
        user.get('middle_name'),  # Now includes middle_name
        user['last_name'],
        # ... rest of params
    )
```

## Error Handling

- All operations are wrapped in transactions
- Failed migrations trigger automatic rollback
- Detailed logging to `migration.log`
- Missing foreign key references are logged as warnings

## Testing

Before running on production data:

1. **Test with a small dataset** first
2. **Backup your PostgreSQL database** before migration
3. **Verify data integrity** after migration
4. **Test application functionality** with migrated data

## Extending the Migration

To add more tables, follow this pattern:

```python
def migrate_your_table(self):
    """Migrate your custom table"""
    logger.info("Starting your_table migration...")

    mysql_query = "SELECT * FROM your_table WHERE deleted_at IS NULL"
    mysql_data = self.get_mysql_data(mysql_query)

    postgres_query = """
    INSERT INTO your_table (id, field1, field2, created_at, updated_at, voided)
    VALUES (%s, %s, %s, %s, %s, %s)
    """

    for record in mysql_data:
        record_id = self.generate_uuid(record['id'])
        self.id_mappings[f"your_table_{record['id']}"] = record_id

        params = (
            record_id,
            record['field1'],
            record['field2'],
            record['created_at'],
            record['updated_at'],
            False  # not voided
        )

        self.execute_postgres_query(postgres_query, params)

    logger.info(f"Migrated {len(mysql_data)} your_table records")
```

Then add it to the `run_migration()` method in the correct order.

## Next Steps

After running the core migration, consider migrating these additional tables based on your needs:

1. **Countries, Regions, Districts** - Geographic data
2. **Organizations** - If you have multi-tenant organizations
3. **Applications & Workflows** - Business process data
4. **Documents & Attachments** - File management
5. **Notifications** - User notification system
6. **Audit Logs** - System activity tracking

## Support

If you need to connect to your actual MySQL database to examine the schema, provide the connection details and I can help customize the migration script to match your exact table structure.