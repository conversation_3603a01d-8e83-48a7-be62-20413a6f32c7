#!/usr/bin/env python3
"""
Test script to validate the MySQL to PostgreSQL migration

This script performs basic validation checks on the migrated data
to ensure the migration completed successfully.
"""

import argparse
import logging
from typing import Dict

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MigrationValidator:
    """Validates the results of the MySQL to PostgreSQL migration"""
    
    def __init__(self, postgres_config: Dict):
        self.postgres_config = postgres_config
        self.postgres_session = None
        
    def connect_database(self):
        """Connect to PostgreSQL database"""
        try:
            postgres_url = f"postgresql://{self.postgres_config['user']}:{self.postgres_config['password']}@{self.postgres_config['host']}:{self.postgres_config['port']}/{self.postgres_config['database']}"
            self.postgres_engine = create_engine(postgres_url)
            Session = sessionmaker(bind=self.postgres_engine)
            self.postgres_session = Session()
            logger.info("Connected to PostgreSQL database")
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            raise
    
    def close_connection(self):
        """Close database connection"""
        if self.postgres_session:
            self.postgres_session.close()
        if hasattr(self, 'postgres_engine'):
            self.postgres_engine.dispose()
    
    def validate_record_counts(self):
        """Validate that records were migrated"""
        logger.info("Validating record counts...")
        
        tables = ['accounts', 'users', 'roles', 'user_roles']
        counts = {}
        
        for table in tables:
            result = self.postgres_session.execute(text(f"SELECT COUNT(*) FROM {table}"))
            count = result.scalar()
            counts[table] = count
            logger.info(f"{table}: {count} records")
        
        return counts
    
    def validate_relationships(self):
        """Validate that relationships are intact"""
        logger.info("Validating relationships...")
        
        # Check user-account relationships
        result = self.postgres_session.execute(text("""
            SELECT COUNT(*) FROM users u 
            JOIN accounts a ON u.account_id = a.id
        """))
        user_account_links = result.scalar()
        logger.info(f"User-Account links: {user_account_links}")
        
        # Check user-role relationships
        result = self.postgres_session.execute(text("""
            SELECT COUNT(*) FROM user_roles ur
            JOIN users u ON ur.user_id = u.id
            JOIN roles r ON ur.role_id = r.id
        """))
        user_role_links = result.scalar()
        logger.info(f"User-Role links: {user_role_links}")
        
        return {
            'user_account_links': user_account_links,
            'user_role_links': user_role_links
        }
    
    def validate_data_integrity(self):
        """Validate data integrity"""
        logger.info("Validating data integrity...")
        
        # Check for users without accounts
        result = self.postgres_session.execute(text("""
            SELECT COUNT(*) FROM users u 
            LEFT JOIN accounts a ON u.account_id = a.id 
            WHERE a.id IS NULL
        """))
        orphaned_users = result.scalar()
        
        # Check for duplicate emails
        result = self.postgres_session.execute(text("""
            SELECT COUNT(*) FROM (
                SELECT email, COUNT(*) as cnt 
                FROM users 
                GROUP BY email 
                HAVING COUNT(*) > 1
            ) duplicates
        """))
        duplicate_emails = result.scalar()
        
        # Check for accounts without handles
        result = self.postgres_session.execute(text("""
            SELECT COUNT(*) FROM accounts 
            WHERE handle IS NULL OR handle = ''
        """))
        accounts_without_handles = result.scalar()
        
        logger.info(f"Orphaned users: {orphaned_users}")
        logger.info(f"Duplicate emails: {duplicate_emails}")
        logger.info(f"Accounts without handles: {accounts_without_handles}")
        
        return {
            'orphaned_users': orphaned_users,
            'duplicate_emails': duplicate_emails,
            'accounts_without_handles': accounts_without_handles
        }
    
    def run_validation(self):
        """Run all validation checks"""
        try:
            self.connect_database()
            
            logger.info("Starting migration validation...")
            
            counts = self.validate_record_counts()
            relationships = self.validate_relationships()
            integrity = self.validate_data_integrity()
            
            # Summary
            logger.info("\n" + "="*50)
            logger.info("MIGRATION VALIDATION SUMMARY")
            logger.info("="*50)
            
            logger.info("Record Counts:")
            for table, count in counts.items():
                logger.info(f"  {table}: {count}")
            
            logger.info("\nRelationships:")
            for rel, count in relationships.items():
                logger.info(f"  {rel}: {count}")
            
            logger.info("\nData Integrity Issues:")
            issues_found = False
            for check, count in integrity.items():
                if count > 0:
                    logger.warning(f"  {check}: {count} (ISSUE)")
                    issues_found = True
                else:
                    logger.info(f"  {check}: {count} (OK)")
            
            if not issues_found:
                logger.info("\n✅ Migration validation completed successfully!")
            else:
                logger.warning("\n⚠️  Migration validation found some issues. Please review.")
            
        except Exception as e:
            logger.error(f"Validation failed: {e}")
            raise
        finally:
            self.close_connection()


def main():
    """Main entry point for the validation script"""
    parser = argparse.ArgumentParser(description='Validate MySQL to PostgreSQL migration')
    
    # PostgreSQL connection parameters
    parser.add_argument('--postgres-host', default='localhost', help='PostgreSQL host (default: localhost)')
    parser.add_argument('--postgres-user', default='postgres', help='PostgreSQL username (default: postgres)')
    parser.add_argument('--postgres-password', required=True, help='PostgreSQL password')
    parser.add_argument('--postgres-db', required=True, help='PostgreSQL database name')
    parser.add_argument('--postgres-port', type=int, default=5432, help='PostgreSQL port (default: 5432)')
    
    args = parser.parse_args()
    
    # PostgreSQL configuration
    postgres_config = {
        'host': args.postgres_host,
        'user': args.postgres_user,
        'password': args.postgres_password,
        'database': args.postgres_db,
        'port': args.postgres_port
    }
    
    # Run validation
    validator = MigrationValidator(postgres_config)
    validator.run_validation()


if __name__ == '__main__':
    main()
