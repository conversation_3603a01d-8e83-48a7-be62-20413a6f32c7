#!/usr/bin/env python3
"""
MySQL to PostgreSQL Migration Script

This script migrates data from MySQL to PostgreSQL for the NGORA system.
It handles the core user-related tables and their relationships.

Usage:
    python mysql_to_postgres_migration.py --mysql-host localhost --mysql-user root --mysql-password password --mysql-db old_db
"""

import argparse
import logging
import sys
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
import hashlib

import mysql.connector
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migration.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class DatabaseMigrator:
    """Minimalistic database migration framework"""

    def __init__(self, mysql_config: Dict, postgres_config: Dict):
        self.mysql_config = mysql_config
        self.postgres_config = postgres_config
        self.mysql_conn = None
        self.postgres_conn = None
        self.id_mappings = {}  # Store old_id -> new_uuid mappings

    def connect_databases(self):
        """Connect to both MySQL and PostgreSQL databases"""
        try:
            # Connect to MySQL
            self.mysql_conn = mysql.connector.connect(**self.mysql_config)
            logger.info("Connected to MySQL database")

            # Connect to PostgreSQL
            self.postgres_conn = psycopg2.connect(**self.postgres_config)
            self.postgres_conn.autocommit = False
            logger.info("Connected to PostgreSQL database")

        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            raise

    def close_connections(self):
        """Close database connections"""
        if self.mysql_conn:
            self.mysql_conn.close()
        if self.postgres_conn:
            self.postgres_conn.close()

    def generate_uuid(self, old_id: Any = None) -> str:
        """Generate UUID for new records"""
        if old_id is not None:
            # Generate deterministic UUID based on old ID for consistency
            namespace = uuid.UUID('12345678-1234-5678-1234-123456789012')
            return str(uuid.uuid5(namespace, str(old_id)))
        return str(uuid.uuid4())

    def get_mysql_data(self, query: str, params: tuple = None) -> List[Dict]:
        """Execute MySQL query and return results"""
        cursor = self.mysql_conn.cursor(dictionary=True)
        cursor.execute(query, params or ())
        results = cursor.fetchall()
        cursor.close()
        return results

    def execute_postgres_query(self, query: str, params: tuple = None):
        """Execute PostgreSQL query"""
        cursor = self.postgres_conn.cursor()
        cursor.execute(query, params or ())
        cursor.close()

    def migrate_accounts(self):
        """Migrate accounts table"""
        logger.info("Starting accounts migration...")

        # Assuming MySQL has a users table that we'll convert to accounts + users
        mysql_query = """
        SELECT id, username, email, created_at, updated_at, status
        FROM users
        WHERE deleted_at IS NULL
        """

        mysql_users = self.get_mysql_data(mysql_query)

        postgres_query = """
        INSERT INTO accounts (id, handle, type, status, created_at, updated_at, voided)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        """

        for user in mysql_users:
            account_id = self.generate_uuid(f"account_{user['id']}")
            self.id_mappings[f"account_{user['id']}"] = account_id

            # Map MySQL status to PostgreSQL enum
            status = 'ACTIVE' if user.get('status', 1) == 1 else 'INACTIVE'

            params = (
                account_id,
                user['username'][:15],  # Truncate to fit handle length
                'USER',  # AccountType.USER
                status,
                user['created_at'],
                user['updated_at'],
                False  # not voided
            )

            self.execute_postgres_query(postgres_query, params)

        logger.info(f"Migrated {len(mysql_users)} accounts")

    def migrate_users(self):
        """Migrate users table"""
        logger.info("Starting users migration...")

        mysql_query = """
        SELECT id, first_name, last_name, email, password, phone, gender,
               created_at, updated_at, email_verified_at
        FROM users
        WHERE deleted_at IS NULL
        """

        mysql_users = self.get_mysql_data(mysql_query)

        postgres_query = """
        INSERT INTO users (id, first_name, middle_name, last_name, email, hashed_password,
                          account_id, is_external, verified, gender, phone, created_at,
                          updated_at, voided)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        for user in mysql_users:
            user_id = self.generate_uuid(user['id'])
            account_id = self.id_mappings.get(f"account_{user['id']}")

            if not account_id:
                logger.warning(f"No account found for user {user['id']}, skipping")
                continue

            self.id_mappings[f"user_{user['id']}"] = user_id

            # Map gender
            gender = None
            if user.get('gender'):
                gender = 'MALE' if user['gender'].upper() in ['M', 'MALE'] else 'FEMALE'

            params = (
                user_id,
                user['first_name'],
                None,  # middle_name - add if exists in MySQL
                user['last_name'],
                user['email'],
                user['password'],  # Assuming already hashed
                account_id,
                True,  # is_external - default
                bool(user.get('email_verified_at')),
                gender,
                user.get('phone'),
                user['created_at'],
                user['updated_at'],
                False  # not voided
            )

            self.execute_postgres_query(postgres_query, params)

        logger.info(f"Migrated {len(mysql_users)} users")

    def migrate_roles(self):
        """Migrate roles table"""
        logger.info("Starting roles migration...")

        mysql_query = """
        SELECT id, name, description, created_at, updated_at
        FROM roles
        WHERE deleted_at IS NULL
        """

        mysql_roles = self.get_mysql_data(mysql_query)

        postgres_query = """
        INSERT INTO roles (id, code, name, description, created_at, updated_at, voided)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        """

        for role in mysql_roles:
            role_id = self.generate_uuid(role['id'])
            self.id_mappings[f"role_{role['id']}"] = role_id

            # Generate code from name if not exists
            code = role.get('code', role['name'].upper().replace(' ', '_'))

            params = (
                role_id,
                code,
                role['name'],
                role.get('description'),
                role['created_at'],
                role['updated_at'],
                False  # not voided
            )

            self.execute_postgres_query(postgres_query, params)

        logger.info(f"Migrated {len(mysql_roles)} roles")

    def migrate_departments(self):
        """Migrate departments table"""
        logger.info("Starting departments migration...")

        mysql_query = """
        SELECT id, name, description, created_at, updated_at
        FROM departments
        WHERE deleted_at IS NULL
        """

        mysql_departments = self.get_mysql_data(mysql_query)

        postgres_query = """
        INSERT INTO departments (id, name, code, description, created_at, updated_at, voided)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        """

        for dept in mysql_departments:
            dept_id = self.generate_uuid(dept['id'])
            self.id_mappings[f"department_{dept['id']}"] = dept_id

            # Generate code from name
            code = dept['name'][:10].upper().replace(' ', '')

            params = (
                dept_id,
                dept['name'],
                code,
                dept.get('description'),
                dept['created_at'],
                dept['updated_at'],
                False  # not voided
            )

            self.execute_postgres_query(postgres_query, params)

        logger.info(f"Migrated {len(mysql_departments)} departments")

    def migrate_user_roles(self):
        """Migrate user-role assignments"""
        logger.info("Starting user_roles migration...")

        mysql_query = """
        SELECT user_id, role_id, created_at, updated_at
        FROM user_roles
        """

        mysql_user_roles = self.get_mysql_data(mysql_query)

        postgres_query = """
        INSERT INTO user_roles (id, user_id, role_id, created_at, updated_at, voided)
        VALUES (%s, %s, %s, %s, %s, %s)
        """

        for ur in mysql_user_roles:
            user_id = self.id_mappings.get(f"user_{ur['user_id']}")
            role_id = self.id_mappings.get(f"role_{ur['role_id']}")

            if not user_id or not role_id:
                logger.warning(f"Missing mapping for user_role: user_id={ur['user_id']}, role_id={ur['role_id']}")
                continue

            params = (
                self.generate_uuid(),
                user_id,
                role_id,
                ur['created_at'],
                ur['updated_at'],
                False  # not voided
            )

            self.execute_postgres_query(postgres_query, params)

        logger.info(f"Migrated {len(mysql_user_roles)} user-role assignments")

    def migrate_user_departments(self):
        """Migrate user-department assignments"""
        logger.info("Starting user_departments migration...")

        mysql_query = """
        SELECT user_id, department_id, created_at, updated_at
        FROM user_departments
        """

        mysql_user_depts = self.get_mysql_data(mysql_query)

        postgres_query = """
        INSERT INTO user_departments (id, user_id, department_id, created_at, updated_at, voided)
        VALUES (%s, %s, %s, %s, %s, %s)
        """

        for ud in mysql_user_depts:
            user_id = self.id_mappings.get(f"user_{ud['user_id']}")
            dept_id = self.id_mappings.get(f"department_{ud['department_id']}")

            if not user_id or not dept_id:
                logger.warning(f"Missing mapping for user_department: user_id={ud['user_id']}, dept_id={ud['department_id']}")
                continue

            params = (
                self.generate_uuid(),
                user_id,
                dept_id,
                ud['created_at'],
                ud['updated_at'],
                False  # not voided
            )

            self.execute_postgres_query(postgres_query, params)

        logger.info(f"Migrated {len(mysql_user_depts)} user-department assignments")

    def run_migration(self):
        """Run the complete migration process"""
        try:
            self.connect_databases()

            # Migration order is important due to foreign key constraints
            self.migrate_accounts()
            self.postgres_conn.commit()

            self.migrate_users()
            self.postgres_conn.commit()

            self.migrate_roles()
            self.postgres_conn.commit()

            self.migrate_departments()
            self.postgres_conn.commit()

            self.migrate_user_roles()
            self.postgres_conn.commit()

            self.migrate_user_departments()
            self.postgres_conn.commit()

            logger.info("Migration completed successfully!")

        except Exception as e:
            logger.error(f"Migration failed: {e}")
            if self.postgres_conn:
                self.postgres_conn.rollback()
            raise
        finally:
            self.close_connections()


def main():
    parser = argparse.ArgumentParser(description='Migrate data from MySQL to PostgreSQL')
    parser.add_argument('--mysql-host', required=True, help='MySQL host')
    parser.add_argument('--mysql-user', required=True, help='MySQL username')
    parser.add_argument('--mysql-password', required=True, help='MySQL password')
    parser.add_argument('--mysql-db', required=True, help='MySQL database name')
    parser.add_argument('--mysql-port', type=int, default=3306, help='MySQL port')

    args = parser.parse_args()

    # MySQL configuration
    mysql_config = {
        'host': args.mysql_host,
        'user': args.mysql_user,
        'password': args.mysql_password,
        'database': args.mysql_db,
        'port': args.mysql_port,
        'charset': 'utf8mb4'
    }

    # PostgreSQL configuration from environment
    postgres_config = {
        'host': os.getenv('POSTGRES_HOST', 'localhost'),
        'user': os.getenv('POSTGRES_USER', 'root'),
        'password': os.getenv('POSTGRES_PASSWORD', '12345'),
        'database': os.getenv('POSTGRES_DB', 'ngora_dev_pdb'),
        'port': int(os.getenv('POSTGRES_PORT', '5432'))
    }

    migrator = DatabaseMigrator(mysql_config, postgres_config)
    migrator.run_migration()


if __name__ == '__main__':
    main()