#!/usr/bin/env python3
"""
MySQL to PostgreSQL Migration Script for NGORA System

This script migrates data from the existing MySQL database (schema.sql)
to the new PostgreSQL schema for the NGORA system.

Based on actual MySQL schema:
- user table -> accounts + users tables
- role table -> roles table
- organization table -> organizations (if needed)
- district table -> districts (if needed)

Usage:
    python mysql_to_postgres_migration.py --mysql-host localhost --mysql-user root --mysql-password password --mysql-db ngora
"""

import argparse
import logging
import sys
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any

import mysql.connector
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv
import os

# Import Pydantic models and services
from src.config.db.models.user import User
from src.config.db.models.account import Account, AccountType, AccountStatus
from src.config.db.models.role import Role
from src.config.db.models.user_role import UserRole
from src.config.db.models.user_department import UserDepartment
from src.config.db.models.base import Gender
from src.modules.users.users_schema import UserBaseDto, AccountBaseDto
from src.modules.roles.roles_schema import RoleCreate
from src.core.services.encryption_service import EncryptionService

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migration.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class DatabaseMigrator:
    """MySQL to PostgreSQL migration framework for NGORA system"""

    def __init__(self, mysql_config: Dict):
        self.mysql_config = mysql_config
        self.mysql_conn = None
        self.postgres_engine = None
        self.postgres_session = None
        self.id_mappings = {}  # Store old_id -> new_uuid mappings
        self.encryption_service = EncryptionService()

    def connect_databases(self):
        """Connect to both MySQL and PostgreSQL databases"""
        try:
            # Connect to MySQL
            self.mysql_conn = mysql.connector.connect(**self.mysql_config)
            logger.info("Connected to MySQL database")

            # Connect to PostgreSQL using SQLAlchemy
            postgres_url = os.getenv("DATABASE_URL")
            self.postgres_engine = create_engine(postgres_url)
            Session = sessionmaker(bind=self.postgres_engine)
            self.postgres_session = Session()
            logger.info("Connected to PostgreSQL database")

        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            raise

    def close_connections(self):
        """Close database connections"""
        if self.mysql_conn:
            self.mysql_conn.close()
        if self.postgres_session:
            self.postgres_session.close()
        if self.postgres_engine:
            self.postgres_engine.dispose()

    def generate_uuid(self, old_id: Any = None, prefix: str = "") -> str:
        """Generate UUID for new records"""
        if old_id is not None:
            # Generate deterministic UUID based on old ID for consistency
            namespace = uuid.UUID('********-1234-5678-1234-************')
            return str(uuid.uuid5(namespace, f"{prefix}_{old_id}"))
        return str(uuid.uuid4())

    def get_mysql_data(self, query: str, params: tuple = None) -> List[Dict]:
        """Execute MySQL query and return results"""
        cursor = self.mysql_conn.cursor(dictionary=True)
        cursor.execute(query, params or ())
        results = cursor.fetchall()
        cursor.close()
        return results

    def migrate_accounts(self):
        """Migrate MySQL user table to PostgreSQL accounts table"""
        logger.info("Starting accounts migration...")

        # Get users from MySQL - these become accounts in PostgreSQL
        mysql_query = """
        SELECT user_id, username, email, captured_date, last_edited_date, status,
               organization_id, is_system_user
        FROM user
        WHERE deleted_date IS NULL AND status = 'active'
        """

        mysql_users = self.get_mysql_data(mysql_query)

        for user in mysql_users:
            account_id = self.generate_uuid(user['user_id'], 'account')
            self.id_mappings[f"account_{user['user_id']}"] = account_id

            # Determine account type
            account_type = AccountType.USER  # Default to USER type
            if user.get('is_system_user') == 'yes':
                account_type = AccountType.USER  # Keep as USER since no SYSTEM type exists

            # Map status
            status = AccountStatus.ACTIVE if user.get('status') == 'active' else AccountStatus.INACTIVE

            # Create Account using SQLAlchemy model
            account = Account(
                id=account_id,
                handle=user['username'][:15] if user['username'] else f"user_{user['user_id']}"[:15],
                type=account_type,
                status=status,
                created_at=user['captured_date'] or datetime.now(),
                updated_at=user['last_edited_date'] or user['captured_date'] or datetime.now(),
                voided=False
            )

            self.postgres_session.add(account)

        self.postgres_session.flush()  # Flush to get IDs
        logger.info(f"Migrated {len(mysql_users)} accounts")

    def migrate_users(self):
        """Migrate MySQL user table to PostgreSQL users table"""
        logger.info("Starting users migration...")

        mysql_query = """
        SELECT user_id, firstname, lastname, email, password, position, organization_id,
               district_id, role_id, is_ngo_user, is_system_user, email_verified,
               captured_date, last_edited_date, status
        FROM user
        WHERE deleted_date IS NULL AND status = 'active'
        """

        mysql_users = self.get_mysql_data(mysql_query)

        for user in mysql_users:
            user_id = self.generate_uuid(user['user_id'], 'user')
            account_id = self.id_mappings.get(f"account_{user['user_id']}")

            if not account_id:
                logger.warning(f"No account found for user {user['user_id']}, skipping")
                continue

            self.id_mappings[f"user_{user['user_id']}"] = user_id

            # Determine if external user (NGO users are external)
            is_external = user.get('is_ngo_user') == 'yes'

            # Email verification status
            verified = user.get('email_verified') == 'yes'

            # Hash the password if it's not already hashed (assuming it needs rehashing)
            hashed_password = user['password']
            if user['password'] and not user['password'].startswith('$2b$'):
                hashed_password = self.encryption_service.hash_password(user['password'])

            # Create User using SQLAlchemy model
            new_user = User(
                id=user_id,
                first_name=user['firstname'] or 'Unknown',
                middle_name=None,  # Not in MySQL schema
                last_name=user['lastname'] or 'User',
                email=user['email'],
                hashed_password=hashed_password,
                account_id=account_id,
                is_external=is_external,
                verified=verified,
                gender=None,  # Not in MySQL schema, will use default
                phone=None,  # Not in MySQL schema
                created_at=user['captured_date'] or datetime.now(),
                updated_at=user['last_edited_date'] or user['captured_date'] or datetime.now(),
                voided=False
            )

            self.postgres_session.add(new_user)

        self.postgres_session.flush()  # Flush to get IDs
        logger.info(f"Migrated {len(mysql_users)} users")

    def migrate_roles(self):
        """Migrate MySQL role table to PostgreSQL roles table"""
        logger.info("Starting roles migration...")

        mysql_query = """
        SELECT role_id, role_name, is_system_role, captured_date, last_edited_date, status
        FROM role
        WHERE deleted_date IS NULL AND status = 'active'
        """

        mysql_roles = self.get_mysql_data(mysql_query)

        for role in mysql_roles:
            role_id = self.generate_uuid(role['role_id'], 'role')
            self.id_mappings[f"role_{role['role_id']}"] = role_id

            # Generate code from role name
            code = role['role_name'].upper().replace(' ', '_')[:20] if role['role_name'] else f"ROLE_{role['role_id']}"

            # Create Role using SQLAlchemy model
            new_role = Role(
                id=role_id,
                code=code,
                name=role['role_name'] or f"Role {role['role_id']}",
                description=f"Migrated role: {role['role_name']}" if role['role_name'] else "Migrated role",
                created_at=role['captured_date'] or datetime.now(),
                updated_at=role['last_edited_date'] or role['captured_date'] or datetime.now(),
                voided=False
            )

            self.postgres_session.add(new_role)

        self.postgres_session.flush()  # Flush to get IDs
        logger.info(f"Migrated {len(mysql_roles)} roles")

    def migrate_user_roles(self):
        """Create user-role associations based on MySQL user.role_id"""
        logger.info("Starting user-role associations migration...")

        mysql_query = """
        SELECT user_id, role_id, captured_date, last_edited_date
        FROM user
        WHERE deleted_date IS NULL AND status = 'active' AND role_id IS NOT NULL
        """

        mysql_user_roles = self.get_mysql_data(mysql_query)

        for user_role in mysql_user_roles:
            user_id = self.id_mappings.get(f"user_{user_role['user_id']}")
            role_id = self.id_mappings.get(f"role_{user_role['role_id']}")

            if not user_id or not role_id:
                logger.warning(f"Missing user or role mapping for user_id={user_role['user_id']}, role_id={user_role['role_id']}")
                continue

            user_role_id = self.generate_uuid(f"{user_role['user_id']}_{user_role['role_id']}", 'user_role')

            # Create UserRole using SQLAlchemy model
            new_user_role = UserRole(
                id=user_role_id,
                user_id=user_id,
                role_id=role_id,
                created_at=user_role['captured_date'] or datetime.now(),
                updated_at=user_role['last_edited_date'] or user_role['captured_date'] or datetime.now(),
                voided=False
            )

            self.postgres_session.add(new_user_role)

        self.postgres_session.flush()  # Flush to get IDs
        logger.info(f"Migrated {len(mysql_user_roles)} user-role associations")

    def run_migration(self):
        """Execute the complete migration process"""
        try:
            self.connect_databases()

            # Start transaction
            self.postgres_session.begin()

            # Run migrations in order
            self.migrate_accounts()
            self.migrate_users()
            self.migrate_roles()
            self.migrate_user_roles()

            # Commit transaction
            self.postgres_session.commit()
            logger.info("Migration completed successfully!")

        except Exception as e:
            logger.error(f"Migration failed: {e}")
            if self.postgres_session:
                self.postgres_session.rollback()
            raise
        finally:
            self.close_connections()


def main():
    """Main entry point for the migration script"""
    parser = argparse.ArgumentParser(description='Migrate data from MySQL to PostgreSQL')

    # MySQL connection parameters
    parser.add_argument('--mysql-host', required=True, help='MySQL host')
    parser.add_argument('--mysql-user', required=True, help='MySQL username')
    parser.add_argument('--mysql-password', required=True, help='MySQL password')
    parser.add_argument('--mysql-db', required=True, help='MySQL database name')
    parser.add_argument('--mysql-port', type=int, default=3306, help='MySQL port (default: 3306)')

    # PostgreSQL connection parameters
    parser.add_argument('--postgres-host', default='localhost', help='PostgreSQL host (default: localhost)')
    parser.add_argument('--postgres-user', default='postgres', help='PostgreSQL username (default: postgres)')
    parser.add_argument('--postgres-password', help='PostgreSQL password')
    parser.add_argument('--postgres-db', required=True, help='PostgreSQL database name')
    parser.add_argument('--postgres-port', type=int, default=5432, help='PostgreSQL port (default: 5432)')

    args = parser.parse_args()

    # MySQL configuration
    mysql_config = {
        'host': args.mysql_host,
        'user': args.mysql_user,
        'password': args.mysql_password,
        'database': args.mysql_db,
        'port': args.mysql_port,
        'charset': 'utf8mb4',
        'use_unicode': True
    }

    # PostgreSQL configuration
    postgres_config = {
        'host': args.postgres_host,
        'user': args.postgres_user,
        'password': args.postgres_password or os.getenv('POSTGRES_PASSWORD'),
        'database': args.postgres_db,
        'port': args.postgres_port
    }

    # Run migration
    migrator = DatabaseMigrator(mysql_config, postgres_config)
    migrator.run_migration()


if __name__ == '__main__':
    main()

    def migrate_roles(self):
        """Migrate MySQL role table to PostgreSQL roles table"""
        logger.info("Starting roles migration...")

        mysql_query = """
        SELECT role_id, role_name, is_system_role, captured_date, last_edited_date, status
        FROM role
        WHERE deleted_date IS NULL AND status = 'active'
        """

        mysql_roles = self.get_mysql_data(mysql_query)

        postgres_query = """
        INSERT INTO roles (id, code, name, description, created_at, updated_at, voided)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        """

        for role in mysql_roles:
            role_id = self.generate_uuid(role['role_id'], 'role')
            self.id_mappings[f"role_{role['role_id']}"] = role_id

            # Generate code from role name
            code = role['role_name'].upper().replace(' ', '_')[:20] if role['role_name'] else f"ROLE_{role['role_id']}"

            params = (
                role_id,
                code,
                role['role_name'],
                f"Migrated role: {role['role_name']}" if role['role_name'] else "Migrated role",
                role['captured_date'] or datetime.now(),
                role['last_edited_date'] or role['captured_date'] or datetime.now(),
                False  # not voided
            )

            self.execute_postgres_query(postgres_query, params)

        logger.info(f"Migrated {len(mysql_roles)} roles")

    def migrate_user_roles(self):
        """Create user-role associations based on MySQL user.role_id"""
        logger.info("Starting user-role associations migration...")

        mysql_query = """
        SELECT user_id, role_id, captured_date, last_edited_date
        FROM user
        WHERE deleted_date IS NULL AND status = 'active' AND role_id IS NOT NULL
        """

        mysql_user_roles = self.get_mysql_data(mysql_query)

        postgres_query = """
        INSERT INTO user_roles (id, user_id, role_id, created_at, updated_at, voided)
        VALUES (%s, %s, %s, %s, %s, %s)
        """

        for user_role in mysql_user_roles:
            user_id = self.id_mappings.get(f"user_{user_role['user_id']}")
            role_id = self.id_mappings.get(f"role_{user_role['role_id']}")

            if not user_id or not role_id:
                logger.warning(f"Missing user or role mapping for user_id={user_role['user_id']}, role_id={user_role['role_id']}")
                continue

            user_role_id = self.generate_uuid(f"{user_role['user_id']}_{user_role['role_id']}", 'user_role')

            params = (
                user_role_id,
                user_id,
                role_id,
                user_role['captured_date'] or datetime.now(),
                user_role['last_edited_date'] or user_role['captured_date'] or datetime.now(),
                False  # not voided
            )

            self.execute_postgres_query(postgres_query, params)

        logger.info(f"Migrated {len(mysql_user_roles)} user-role associations")

    def run_migration(self):
        """Execute the complete migration process"""
        try:
            self.connect_databases()

            # Start transaction
            cursor = self.postgres_conn.cursor()
            cursor.execute("BEGIN")
            cursor.close()

            # Run migrations in order
            self.migrate_accounts()
            self.migrate_users()
            self.migrate_roles()
            self.migrate_user_roles()

            # Commit transaction
            self.postgres_conn.commit()
            logger.info("Migration completed successfully!")

        except Exception as e:
            logger.error(f"Migration failed: {e}")
            if self.postgres_conn:
                self.postgres_conn.rollback()
            raise
        finally:
            self.close_connections()


def main():
    """Main entry point for the migration script"""
    parser = argparse.ArgumentParser(description='Migrate data from MySQL to PostgreSQL')

    # MySQL connection parameters
    parser.add_argument('--mysql-host', required=True, help='MySQL host')
    parser.add_argument('--mysql-user', required=True, help='MySQL username')
    parser.add_argument('--mysql-password', required=True, help='MySQL password')
    parser.add_argument('--mysql-db', required=True, help='MySQL database name')
    parser.add_argument('--mysql-port', type=int, default=3306, help='MySQL port (default: 3306)')

    args = parser.parse_args()

    # MySQL configuration
    mysql_config = {
        'host': args.mysql_host,
        'user': args.mysql_user,
        'password': args.mysql_password,
        'database': args.mysql_db,
        'port': args.mysql_port,
        'charset': 'utf8mb4',
        'use_unicode': True
    }

    # Run migration
    migrator = DatabaseMigrator(mysql_config)
    migrator.run_migration()


if __name__ == '__main__':
    main()