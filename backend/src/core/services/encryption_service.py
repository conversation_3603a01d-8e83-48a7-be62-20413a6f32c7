from passlib.hash import bcrypt
import secrets
import base64
import jwt
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Tuple

from src.config import settings


class EncryptionService:
	"""
	Service for handling all encryption-related operations including
	password hashing and JWT token generation/verification.
	"""

	def __init__(self):
		self.secret_key = settings.SECRET_KEY
		self.algorithm = "HS256"
		self.access_token_expire_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
		self.refresh_token_expire_days = settings.REFRESH_TOKEN_EXPIRE_DAYS

	def hash_password(self, password: str) -> str:
		"""
		Hash a password using bcrypt.

		Args:
			password: The plaintext password to hash

		Returns:
			Hashed password
		"""
		return bcrypt.hash(password)

	def verify_password(self, password: str, hashed_password: str) -> bool:
		"""
		Verify a password against its hash.

		Args:
			password: The plaintext password to verify
			hashed_password: The hashed password to check against

		Returns:
			<PERSON><PERSON><PERSON> indicating if the password is correct
		"""
		return bcrypt.verify(password, hash=hashed_password)

	def create_access_token(self, data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
		"""
		Create a JWT access token.

		Args:
			data: Payload data to encode in the token
			expires_delta: Optional custom expiration time

		Returns:
			JWT token string
		"""
		to_encode = data.copy()

		if expires_delta:
			expire = datetime.now() + expires_delta
		else:
			expire = datetime.now() + timedelta(minutes=self.access_token_expire_minutes)

		to_encode.update({"exp": expire, "type": "access"})

		encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
		return encoded_jwt

	def create_refresh_token(self, data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
		"""
		Create a JWT refresh token with longer expiration.

		Args:
			data: Payload data to encode in the token

		Returns:
			JWT refresh token string
		"""
		to_encode = data.copy()

		if expires_delta:
			expire = datetime.now() + expires_delta
		else:
			# Default to refresh token expiration days
			expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
		to_encode.update({"exp": expire, "type": "refresh"})

		encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
		return encoded_jwt

	def generate_tokens(
		self,
		user_id: str,
		additional_data: Optional[Dict[str, Any]] = None,
		access_token: Optional[timedelta] = None,
		refresh_token: Optional[timedelta] = None,
	) -> Dict[str, str]:
		"""
		Generate both access and refresh tokens for a user.

		Args:
			user_id: The ID of the user to generate tokens for
			additional_data: Optional additional claims to include in the token

		Returns:
			Dictionary containing both tokens
		"""
		data = {"sub": user_id}
		if additional_data:
			data.update(additional_data)

		access_token_obj = self.create_access_token(data, expires_delta=access_token)
		refresh_token_obj = self.create_refresh_token(data, expires_delta=refresh_token)

		return {"access_token": access_token_obj, "refresh_token": refresh_token_obj, "token_type": "bearer"}

	def verify_token(self, token: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
		"""
		Verify and decode a JWT token.

		Args:
			token: The JWT token to verify

		Returns:
			Tuple of (is_valid, payload) where payload is None if invalid
		"""
		try:
			payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
			return True, payload
		except jwt.PyJWTError:
			return False, None

	def refresh_access_token(self, refresh_token: str) -> Optional[str]:
		"""
		Use a refresh token to generate a new access token.

		Args:
			refresh_token: The refresh token to use

		Returns:
			New access token or None if refresh token is invalid
		"""
		is_valid, payload = self.verify_token(refresh_token)

		if not is_valid or payload.get("type") != "refresh":
			return None

		# Remove the expiration and token type for the new token
		payload.pop("exp", None)
		payload.pop("type", None)

		# Create new access token
		return self.create_access_token(payload)

	def generate_password_reset_token(self, email: str) -> str:
		"""
		Generate a password reset token.

		Args:
			email: The user's email

		Returns:
			Password reset token
		"""
		expire = datetime.utcnow() + timedelta(hours=24)
		to_encode = {"sub": email, "exp": expire, "type": "reset"}
		return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)

	def verify_password_reset_token(self, token: str) -> Optional[str]:
		"""
		Verify a password reset token and return the user's email.

		Args:
			token: The password reset token

		Returns:
			The user's email if valid, None otherwise
		"""
		is_valid, payload = self.verify_token(token)
		if not is_valid or payload.get("type") != "reset":
			return None

		return payload.get("sub")

	# Add these methods to your EncryptionService class

	def generate_email_verification_token(self, email: str) -> str:
		"""Generate email verification token"""
		payload = {
			"email": email,
			"type": "email_verification",
			"exp": datetime.utcnow() + timedelta(hours=24),  # 24-hour expiry
			"iat": datetime.utcnow(),
		}

		return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)

	def verify_email_verification_token(self, token: str) -> str:
		"""Verify email verification token and return email"""
		try:
			payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])

			if payload.get("type") != "email_verification":
				raise jwt.InvalidTokenError("Invalid token type")

			email = payload.get("email")
			if not email:
				raise jwt.InvalidTokenError("Email not found in token")

			return email

		except jwt.ExpiredSignatureError:
			raise Exception("Verification token has expired")
		except jwt.InvalidTokenError:
			raise Exception("Invalid verification token")

	def generate_random_secure_string(self, length: int = 32) -> str:
		"""
		Generate a cryptographically secure random string.

		Args:
			length: Length of the byte string before encoding

		Returns:
			URL-safe base64 encoded random string
		"""
		token_bytes = secrets.token_bytes(length)
		return base64.urlsafe_b64encode(token_bytes).decode().rstrip("=")


encryption_service = EncryptionService()
