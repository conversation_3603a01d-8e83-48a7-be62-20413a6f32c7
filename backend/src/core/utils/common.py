from typing import Optional, Type

from pydantic import BaseModel


def capitalize_words(name: str) -> str:
	"""Capitalize each word in a given word properly."""
	return " ".join(word.capitalize() for word in name.split())


def get_clean_filters(filters: dict) -> dict:
	return {k: v for k, v in filters.dict(exclude_unset=True).items() if v not in (None, "")}


def super_to_optional(model: Type[BaseModel]) -> Type[BaseModel]:
	"""
	Convert a Pydantic model to a new model where all fields are optional.

	This function takes a Pydantic model class and returns a new model class
	with the same fields, but all fields are made optional. The new model
	class is dynamically created and inherits from `BaseModel`.

	Args:
	    model (Type[BaseModel]): The Pydantic model class to convert.

	Returns:
	    Type[BaseModel]: A new Pydantic model class with all fields optional.
	"""
	annotations = {name: Optional[field.annotation] for name, field in model.model_fields.items()}
	return type(
		f"Optional{model.__name__}",
		(BaseModel,),
		{
			"__annotations__": annotations,
			**{name: None for name in model.model_fields},
		},
	)
