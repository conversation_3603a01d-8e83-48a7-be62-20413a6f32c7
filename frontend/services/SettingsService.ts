import api from "@/config/api.config";
import http from "@/config/http";
import {
    CountryDto,
    CountryFilter,
    DistrictDto,
    DistrictFilter,
    HttpResponse,
    LoadableItemDto,
    LoadableItemFilter,
    RegionDto,
    RegionFilter,
} from "@/types";
import { httpResponse, jsonToQueryParams } from "@/utils/common";

export const fetchLoadableItems = async (
    filter: Partial<LoadableItemFilter> = {},
): Promise<HttpResponse<LoadableItemDto[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.settings.loadable_items.root}${queryString ? `?${queryString}` : ""}`;

        const response = await http.get(url);

        return httpResponse<LoadableItemDto[]>(response);
    } catch (error) {
        return httpResponse(error);
    }
};

export const fetchCountries = async (
    filter: Partial<CountryFilter> = {},
): Promise<HttpResponse<CountryDto[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.settings.countries}${queryString ? `?${queryString}` : ""}`;

        const response = await http.get(url);

        return httpResponse<CountryDto[]>(response);
    } catch (error) {
        return httpResponse(error);
    }
};

export const fetchDistricts = async (
    filter: Partial<DistrictFilter> = {},
): Promise<HttpResponse<DistrictDto[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.settings.districts}${queryString ? `?${queryString}` : ""}`;

        const response = await http.get(url);

        return httpResponse<DistrictDto[]>(response);
    } catch (error) {
        return httpResponse(error);
    }
};

export const fetchRegions = async (
    filter: Partial<RegionFilter> = {},
): Promise<HttpResponse<RegionDto[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.settings.regions}${queryString ? `?${queryString}` : ""}`;

        const response = await http.get(url);

        return httpResponse<RegionDto[]>(response);
    } catch (error) {
        return httpResponse(error);
    }
};
