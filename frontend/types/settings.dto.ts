import { Pagination } from "@/types/common.type";

export interface LoadableItemDto {
    id: string;
    type: string;
    code: string;
    display_value: string;
    description: string;
    created_at: Date;
    updated_at: Date;
}

export interface LoadableItemFilter extends Pagination {
    type?: string;
    code?: string;
    display_value?: string;
}

export interface CountryFilter extends Pagination {
    name?: string;
}

export interface CountryDto {
    id: string;
    name: string;
    dial_code: string;
    short_code: string;
    flag: string;
}

export interface DistrictDto {
    id: string;
    name: string;
    code: string;
    region_id: string;
    created_at: string;
    updated_at: string;
    region_name: string;
}

export interface RegionDto {
    id: string;
    name: string;
    code: string;
    created_at: string;
    updated_at: string;
}

export interface DistrictFilter {
    name?: string;
    region_id?: string;
}

export interface RegionFilter {
    name?: string;
}
