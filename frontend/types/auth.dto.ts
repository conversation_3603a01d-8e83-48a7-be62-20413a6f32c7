import { Gender } from "./user.type";

export type AuthType = "MAIN" | "VERIFICATION" | "LOGIN_2FA";

export interface AuthDto {
    first_name: string;
    last_name: string;
    middle_name?: string | null;
    username: string;
    email: string;
    phone?: string | null;
    gender: Gender;
    user_id: string;
    role_id?: string | null;
    role_name?: string | null;
    permissions: string[];
    auth_type: AuthType;
    name: string;
}
