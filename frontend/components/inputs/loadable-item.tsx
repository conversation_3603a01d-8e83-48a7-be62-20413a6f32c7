"use client";

import { Autocomplete, AutocompleteItem } from "@heroui/autocomplete";
import { Spinner } from "@heroui/spinner";
import { useRef, useState } from "react";

import { fetchLoadableItems } from "@/services/SettingsService";
import { LoadableItemDto } from "@/types";
import { AsyncAutocompleteProps } from "@/types/input.dto";

type InputProps = AsyncAutocompleteProps & {
    type?: string;
};

export default function LoadableItemInput({ ...props }: InputProps) {
    const [isLoading, setIsLoading] = useState(false);
    const [loadableItems, setLoadableItems] = useState<LoadableItemDto[]>([]);
    const debounceTimeout = useRef<NodeJS.Timeout>();

    const handleInputChange = (value: string) => {
        if (debounceTimeout.current) {
            clearTimeout(debounceTimeout.current);
        }

        setIsLoading(true);

        debounceTimeout.current = setTimeout(async () => {
            try {
                const results = await fetchLoadableItems({
                    type: props.type,
                    display_value: value,
                    size: 20,
                });

                if (results.data) {
                    setLoadableItems(results.data);
                }
            } catch (error) {
                console.error("Error fetching loadable items:", error);
            } finally {
                setIsLoading(false);
            }
        }, 300);
    };

    return (
        <Autocomplete
            className={props.className}
            defaultItems={loadableItems}
            endContent={isLoading && <Spinner size="sm" />}
            errorMessage={props.errorMessage}
            isDisabled={props.isDisabled}
            isInvalid={props.isInvalid}
            isRequired={props.isRequired}
            label={props.label}
            placeholder={props.placeholder}
            value={props.value}
            onInputChange={handleInputChange}
            onSelectionChange={props.onSelectionChange}
        >
            {(item) => (
                <AutocompleteItem key={item.id} textValue={item.display_value}>
                    <span className="font-lg text-default-900">
                        {item.display_value}
                    </span>
                </AutocompleteItem>
            )}
        </Autocomplete>
    );
}
