"use client";

import * as React from "react";

import { InputOTP, InputOTPGroup, InputOTPSeparator, InputOTPSlot } from "@/components/ui/input-otp";
import { cn } from "@/lib/utils";

interface OTPInputProps {
    length?: number;
    value: string;
    onChange: (value: string) => void;
    className?: string;
    groupSize?: number;
    autoFocus?: boolean;
    disabled?: boolean;
}

const OneTimePassword = React.forwardRef<React.ElementRef<typeof InputOTP>, OTPInputProps>(
    ({ length = 6, value, onChange, className, groupSize = 3, autoFocus = false, disabled = false, ...props }, ref) => {
        // Calculate how many groups we need and their sizes
        const createGroups = () => {
            const groups = [];
            const totalSlots = length;

            // If length is 4 or less, put all in one group
            if (totalSlots <= 4) {
                const slots = [];

                for (let i = 0; i < totalSlots; i++) {
                    slots.push(<InputOTPSlot key={i} index={i} />);
                }
                groups.push(<InputOTPGroup key="group-0">{slots}</InputOTPGroup>);

                return groups;
            }

            // For more than 4, create groups with separators
            let currentIndex = 0;
            let groupIndex = 0;

            while (currentIndex < totalSlots) {
                const remainingSlots = totalSlots - currentIndex;
                const currentGroupSize = Math.min(groupSize, remainingSlots);

                const slots = [];

                for (let i = 0; i < currentGroupSize; i++) {
                    slots.push(<InputOTPSlot key={currentIndex + i} className="uppercase" index={currentIndex + i} />);
                }

                groups.push(<InputOTPGroup key={`group-${groupIndex}`}>{slots}</InputOTPGroup>);

                currentIndex += currentGroupSize;
                groupIndex++;

                if (currentIndex < totalSlots) {
                    groups.push(<InputOTPSeparator key={`separator-${groupIndex}`} />);
                }
            }

            return groups;
        };

        return (
            <InputOTP
                ref={ref}
                className={cn("", className)}
                disabled={disabled}
                maxLength={length}
                value={value}
                onChange={onChange}
                {...props}
            >
                {createGroups()}
            </InputOTP>
        );
    },
);

OneTimePassword.displayName = "OneTimePassword";

export { OneTimePassword };
