"use client";

import {
    Bot,
    Building2Icon,
    DatabaseIcon,
    GalleryVerticalEnd,
    GroupIcon,
    LucideIcon,
    Settings2,
    Users2Icon,
} from "lucide-react";
import * as React from "react";

import { AccountSwitcher } from "./account-switcher";
import { NavAdmin } from "./nav-admin";

import { NavMain } from "@/components/nav-main";
import { NavUser } from "@/components/nav-user";
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarHeader,
    SidebarRail,
} from "@/components/ui/sidebar";
import { useAuth } from "@/composables/useStore";

type SidebarNavItem = {
    title: string;
    url: string;
    items?: Array<{ title: string; url: string }>;
    icon?: LucideIcon;
};

type AdminLink = {
    name: string;
    url: string;
    icon: LucideIcon;
};

type SidebarProps = {
    accounts: Array<{ name: string; logo: React.ElementType }>;
    navMain: SidebarNavItem[];
    adminLinks: AdminLink[];
};

const data: SidebarProps = {
    accounts: [],
    navMain: [
        {
            title: "Organizations",
            url: "#",
            icon: Building2Icon,
            items: [],
        },
        {
            title: "Applications",
            url: "#",
            icon: Bot,
            items: [
                {
                    title: "New",
                    url: "#",
                },
                {
                    title: "Pending",
                    url: "#",
                },
                {
                    title: "Rejected",
                    url: "#",
                },
            ],
        },
        {
            title: "Settings",
            url: "#",
            icon: Settings2,
            items: [
                {
                    title: "General",
                    url: "#",
                },
            ],
        },
    ],
    adminLinks: [
        {
            name: "Users",
            url: "#",
            icon: Users2Icon,
        },
        {
            name: "Metadata",
            url: "#",
            icon: DatabaseIcon,
        },
        {
            name: "Departments",
            url: "#",
            icon: GroupIcon,
        },
    ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
    const { session } = useAuth();

    data.accounts.push({
        name: `${session?.name}`,
        logo: GalleryVerticalEnd,
    });

    return (
        <Sidebar collapsible="icon" {...props}>
            <SidebarHeader>
                <AccountSwitcher accounts={data.accounts} />
            </SidebarHeader>
            <SidebarContent>
                <NavMain items={data.navMain} />
                <NavAdmin links={data.adminLinks} />
            </SidebarContent>
            {session ? (
                <SidebarFooter>
                    <NavUser session={session} />
                </SidebarFooter>
            ) : (
                ""
            )}
            <SidebarRail />
        </Sidebar>
    );
}
