// =============================================================================
// USER FORM COMPONENT
// =============================================================================

import React, { useState } from "react";

import { Alert } from "../ui.old/Alert";
import { Button } from "../ui.old/Button";
import { Card } from "../ui.old/Card";
import { Input } from "../ui.old/Input";
import { Select } from "../ui.old/Select";

interface UserFormData {
    first_name: string;
    last_name: string;
    middle_name?: string;
    username: string;
    email: string;
    phone?: string;
    password?: string;
    confirm_password?: string;
    role_id?: string;
    department_id?: string;
    is_external: boolean;
    gender?: "MALE" | "FEMALE";
}

interface Role {
    id: string;
    name: string;
    code: string;
}

interface Department {
    id: string;
    name: string;
    code: string;
}

interface UserFormProps {
    initialData?: Partial<UserFormData>;
    roles?: Role[];
    departments?: Department[];
    isEdit?: boolean;
    loading?: boolean;
    error?: string;
    onSubmit?: (data: UserFormData) => Promise<void>;
    onCancel?: () => void;
}

export const UserForm: React.FC<UserFormProps> = ({
    initialData = {},
    roles = [],
    departments = [],
    isEdit = false,
    loading = false,
    error,
    onSubmit,
    onCancel,
}) => {
    const [formData, setFormData] = useState<UserFormData>({
        first_name: "",
        last_name: "",
        middle_name: "",
        username: "",
        email: "",
        phone: "",
        password: "",
        confirm_password: "",
        role_id: "",
        department_id: "",
        is_external: true,
        gender: undefined,
        ...initialData,
    });

    const [errors, setErrors] = useState<Record<string, string>>({});

    const genderOptions = [
        { value: "", label: "Select Gender" },
        { value: "MALE", label: "Male" },
        { value: "FEMALE", label: "Female" },
        { value: "OTHER", label: "Other" },
    ];

    const roleOptions = [
        { value: "", label: "Select Role" },
        ...roles.map((role) => ({ value: role.id, label: role.name })),
    ];

    const departmentOptions = [
        { value: "", label: "Select Department" },
        ...departments.map((dept) => ({ value: dept.id, label: dept.name })),
    ];

    const validateForm = (): boolean => {
        const newErrors: Record<string, string> = {};

        // Required fields
        if (!formData.first_name.trim())
            newErrors.first_name = "First name is required";
        if (!formData.last_name.trim())
            newErrors.last_name = "Last name is required";
        if (!formData.username.trim())
            newErrors.username = "Username is required";
        if (!formData.email.trim()) newErrors.email = "Email is required";

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if (formData.email && !emailRegex.test(formData.email)) {
            newErrors.email = "Please enter a valid email address";
        }

        // Password validation (only for new users or when password is provided)
        if (!isEdit) {
            if (!formData.password) newErrors.password = "Password is required";
            if (!formData.confirm_password)
                newErrors.confirm_password = "Please confirm password";
        }

        if (formData.password && formData.password.length < 8) {
            newErrors.password = "Password must be at least 8 characters long";
        }

        if (
            formData.password &&
            formData.confirm_password &&
            formData.password !== formData.confirm_password
        ) {
            newErrors.confirm_password = "Passwords do not match";
        }

        // Phone validation (optional)
        if (formData.phone && formData.phone.length < 10) {
            newErrors.phone = "Please enter a valid phone number";
        }

        setErrors(newErrors);

        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) return;

        try {
            await onSubmit?.(formData);
        } catch (err) {
            throw err;
            // Error handling done by parent
        }
    };

    const updateField = (field: keyof UserFormData, value: any) => {
        setFormData((prev) => ({ ...prev, [field]: value }));
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors((prev) => ({ ...prev, [field]: "" }));
        }
    };

    return (
        <Card title={isEdit ? "Edit User" : "Create New User"}>
            {error && (
                <Alert
                    dismissible
                    className="mb-6"
                    message={error}
                    type="error"
                />
            )}

            <form className="space-y-6" onSubmit={handleSubmit}>
                {/* Personal Information */}
                <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-4">
                        Personal Information
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Input
                            required
                            disabled={loading}
                            error={errors.first_name}
                            label="First Name"
                            value={formData.first_name}
                            onChange={(value) =>
                                updateField("first_name", value)
                            }
                        />
                        <Input
                            required
                            disabled={loading}
                            error={errors.last_name}
                            label="Last Name"
                            value={formData.last_name}
                            onChange={(value) =>
                                updateField("last_name", value)
                            }
                        />
                        <Input
                            disabled={loading}
                            error={errors.middle_name}
                            label="Middle Name"
                            value={formData.middle_name}
                            onChange={(value) =>
                                updateField("middle_name", value)
                            }
                        />
                        <Select
                            disabled={loading}
                            error={errors.gender}
                            label="Gender"
                            options={genderOptions}
                            value={formData.gender || ""}
                            onChange={(value) => updateField("gender", value)}
                        />
                    </div>
                </div>

                {/* Contact Information */}
                <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-4">
                        Contact Information
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Input
                            required
                            disabled={loading}
                            error={errors.email}
                            label="Email"
                            type="email"
                            value={formData.email}
                            onChange={(value) => updateField("email", value)}
                        />
                        <Input
                            disabled={loading}
                            error={errors.phone}
                            label="Phone Number"
                            type="tel"
                            value={formData.phone}
                            onChange={(value) => updateField("phone", value)}
                        />
                    </div>
                </div>

                {/* Account Information */}
                <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-4">
                        Account Information
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Input
                            required
                            disabled={loading}
                            error={errors.username}
                            label="Username"
                            value={formData.username}
                            onChange={(value) => updateField("username", value)}
                        />
                        <div className="flex items-center mt-6">
                            <input
                                checked={formData.is_external}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                disabled={loading}
                                id="is_external"
                                type="checkbox"
                                onChange={(e) =>
                                    updateField("is_external", e.target.checked)
                                }
                            />
                            <label
                                className="ml-2 block text-sm text-gray-900"
                                htmlFor="is_external"
                            >
                                External User
                            </label>
                        </div>
                    </div>
                </div>

                {/* Password (only for new users or password change) */}
                {!isEdit && (
                    <div>
                        <h4 className="text-lg font-medium text-gray-900 mb-4">
                            Password
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <Input
                                required
                                showPasswordToggle
                                disabled={loading}
                                error={errors.password}
                                label="Password"
                                type="password"
                                value={formData.password}
                                onChange={(value) =>
                                    updateField("password", value)
                                }
                            />
                            <Input
                                required
                                showPasswordToggle
                                disabled={loading}
                                error={errors.confirm_password}
                                label="Confirm Password"
                                type="password"
                                value={formData.confirm_password}
                                onChange={(value) =>
                                    updateField("confirm_password", value)
                                }
                            />
                        </div>
                    </div>
                )}

                {/* Role and Department */}
                <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-4">
                        Organization
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Select
                            disabled={loading}
                            error={errors.role_id}
                            label="Role"
                            options={roleOptions}
                            value={formData.role_id || ""}
                            onChange={(value) => updateField("role_id", value)}
                        />
                        <Select
                            disabled={loading}
                            error={errors.department_id}
                            label="Department"
                            options={departmentOptions}
                            value={formData.department_id || ""}
                            onChange={(value) =>
                                updateField("department_id", value)
                            }
                        />
                    </div>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <Button
                        disabled={loading}
                        type="button"
                        variant="outline"
                        onClick={onCancel}
                    >
                        Cancel
                    </Button>
                    <Button
                        disabled={loading}
                        loading={loading}
                        type="submit"
                        variant="primary"
                    >
                        {isEdit ? "Update User" : "Create User"}
                    </Button>
                </div>
            </form>
        </Card>
    );
};
