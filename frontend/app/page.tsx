"use client";

import {
    <PERSON>R<PERSON>,
    Check,
    ChevronDown,
    ChevronRight,
    Clock,
    ExternalLink,
    FileText,
    Filter,
    Globe,
    Mail,
    MapPin,
    Menu,
    Phone,
    Search,
    Target,
    Users,
    X,
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export default function Home() {
    const [searchTerm, setSearchTerm] = useState("");
    const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
    const [isFilterMenuOpen, setIsFilterMenuOpen] = useState(false);
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

    const handleFilterToggle = (filter: string) => {
        if (selectedFilters.includes(filter)) {
            setSelectedFilters(selectedFilters.filter((f) => f !== filter));
        } else {
            setSelectedFilters([...selectedFilters, filter]);
        }
    };

    const removeFilter = (filter: string) => {
        setSelectedFilters(selectedFilters.filter((f) => f !== filter));
    };

    const filterOptions = [
        { id: "ngo", label: "NGO", icon: <Users size={16} /> },
        { id: "cbo", label: "CBO", icon: <Users size={16} /> },
        {
            id: "international",
            label: "International",
            icon: <Globe size={16} />,
        },
        { id: "local", label: "Local", icon: <MapPin size={16} /> },
        { id: "education", label: "Education", icon: <FileText size={16} /> },
        { id: "health", label: "Health", icon: <FileText size={16} /> },
        {
            id: "environment",
            label: "Environment",
            icon: <FileText size={16} />,
        },
        { id: "children", label: "Children", icon: <Target size={16} /> },
        { id: "women", label: "Women", icon: <Target size={16} /> },
        { id: "elderly", label: "Elderly", icon: <Target size={16} /> },
    ];

    return (
        <div className="min-h-screen bg-white text-gray-900">
            {/* Header - More professional styling */}
            <header className="sticky top-0 z-50 bg-white border-b border-gray-100 shadow-sm">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-20">
                        <div className="flex items-center space-x-3">
                            <div className="relative">
                                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center transform rotate-45 shadow-md">
                                    <span className="text-white font-bold text-xl transform -rotate-45">
                                        N
                                    </span>
                                </div>
                                <div className="absolute -bottom-1 -right-1 h-5 w-5 bg-blue-500 rounded-full border-2 border-white" />
                            </div>
                            <div>
                                <div className="text-xl font-bold text-blue-600">
                                    <span className="font-light">my</span>NGO
                                </div>
                                <span className="text-xs text-gray-500 tracking-wider uppercase">
                                    Regulatory Authority
                                </span>
                            </div>
                        </div>

                        {/* Desktop navigation - Refined styling */}
                        <nav className="hidden md:flex space-x-8">
                            <Link
                                className="font-medium text-gray-700 hover:text-blue-600 transition-colors relative group"
                                href="/"
                            >
                                Home
                                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full" />
                            </Link>
                            <Link
                                className="font-medium text-gray-700 hover:text-blue-600 transition-colors relative group"
                                href="about"
                            >
                                About
                                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full" />
                            </Link>
                            <Link
                                className="font-medium text-gray-700 hover:text-blue-600 transition-colors relative group"
                                href="/organizations"
                            >
                                Organizations
                                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full" />
                            </Link>
                            <Link
                                className="font-medium text-gray-700 hover:text-blue-600 transition-colors relative group"
                                href="/resources"
                            >
                                Resources
                                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full" />
                            </Link>
                            <Link
                                className="font-medium text-gray-700 hover:text-blue-600 transition-colors relative group"
                                href="/contact"
                            >
                                Contact
                                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full" />
                            </Link>
                        </nav>

                        <div className="flex gap-4 items-center">
                            <Link
                                className="hidden md:inline-flex px-5 py-2.5 font-medium text-sm rounded-lg text-blue-600 border border-blue-200 hover:border-blue-300 hover:shadow-sm transition-all"
                                href="/auth"
                            >
                                Sign In
                            </Link>
                            <Link
                                className="hidden md:inline-flex px-5 py-2.5 font-medium text-sm rounded-lg text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-sm hover:shadow transition-all"
                                href="/auth/register"
                            >
                                Register
                            </Link>

                            {/* Mobile menu button */}
                            <button
                                className="md:hidden rounded-md p-2 text-gray-700 hover:bg-gray-100"
                                onClick={() =>
                                    setMobileMenuOpen(!mobileMenuOpen)
                                }
                            >
                                <Menu size={24} />
                            </button>
                        </div>
                    </div>

                    {/* Mobile menu */}
                    {mobileMenuOpen && (
                        <div className="md:hidden py-4 border-t border-gray-100">
                            <nav className="flex flex-col space-y-4">
                                <Link
                                    className="font-medium text-gray-700 hover:text-blue-600 transition-colors pl-2 border-l-2 border-transparent hover:border-blue-600"
                                    href="/"
                                >
                                    Home
                                </Link>
                                <Link
                                    className="font-medium text-gray-700 hover:text-blue-600 transition-colors pl-2 border-l-2 border-transparent hover:border-blue-600"
                                    href="/about"
                                >
                                    About
                                </Link>
                                <Link
                                    className="font-medium text-gray-700 hover:text-blue-600 transition-colors pl-2 border-l-2 border-transparent hover:border-blue-600"
                                    href="/organizations"
                                >
                                    Organizations
                                </Link>
                                <Link
                                    className="font-medium text-gray-700 hover:text-blue-600 transition-colors pl-2 border-l-2 border-transparent hover:border-blue-600"
                                    href="/resources"
                                >
                                    Resources
                                </Link>
                                <Link
                                    className="font-medium text-gray-700 hover:text-blue-600 transition-colors pl-2 border-l-2 border-transparent hover:border-blue-600"
                                    href="/contact"
                                >
                                    Contact
                                </Link>
                                <div className="flex flex-col space-y-2 pt-2 border-t border-gray-100">
                                    <Link
                                        className="px-4 py-2 font-medium text-sm rounded-lg text-blue-600 border border-blue-200 hover:border-blue-300 hover:shadow-sm transition-all text-center"
                                        href="/auth"
                                    >
                                        Sign In
                                    </Link>
                                    <Link
                                        className="px-4 py-2 font-medium text-sm rounded-lg text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 transition-colors text-center"
                                        href="/auth/register"
                                    >
                                        Register
                                    </Link>
                                </div>
                            </nav>
                        </div>
                    )}
                </div>
            </header>

            {/* Hero Section - Professional with unique elements */}
            <section className="relative bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900 text-white overflow-hidden">
                {/* Unique background pattern */}
                <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiIGZpbGwtb3BhY2l0eT0iMC4xIj48cGF0aCBkPSJNMzYgMzRjMC0yLjIxLTEuNzktNC00LTRzLTQgMS43OS00IDRjMCAyLjIxIDEuNzkgNCA0IDRzNC0xLjc5IDQtNHptLTE3LjU4IDYuNThjMS4zNSAxLjM1IDMuNTQgMS4zNSA0Ljg5IDBsMTIuMDItMTIuMDJjMS4zNS0xLjM1IDEuMzUtMy41NCAwLTQuODlMMjMuMzEgMTEuNjVjLTEuMzUtMS4zNS0zLjU0LTEuMzUtNC44OSAwTDYuNCAyMy42N2MtMS4zNSAxLjM1LTEuMzUgMy41NCAwIDQuODlsMTIuMDIgMTIuMDJ6Ii8+PC9nPjwvZz48L3N2Zz4=')] opacity-30 bg-repeat mix-blend-overlay" />

                {/* Distinctive accents */}
                <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-blue-400 via-purple-500 to-blue-400" />
                <div className="absolute -bottom-5 -left-5 h-48 w-48 rounded-full bg-blue-500/20 blur-3xl" />
                <div className="absolute -top-5 -right-5 h-48 w-48 rounded-full bg-blue-400/20 blur-3xl" />

                {/* Content with refined layout */}
                <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-20 sm:py-28">
                    <div className="grid md:grid-cols-2 gap-12 items-center">
                        <div>
                            <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-400/30 text-blue-100 mb-6">
                                <span className="w-2 h-2 bg-blue-300 rounded-full mr-2" />
                                Empowering Global Change
                            </div>
                            <h1 className="text-4xl sm:text-5xl font-bold tracking-tight leading-tight">
                                Find and Connect with{" "}
                                <span className="text-blue-300">
                                    Organizations
                                </span>{" "}
                                That Matter
                            </h1>
                            <p className="mt-6 text-xl leading-relaxed text-blue-100 border-l-4 border-blue-500/30 pl-4">
                                Search our comprehensive database of registered
                                non-governmental organizations operating across
                                various sectors and locations.
                            </p>
                            <div className="mt-10 flex flex-col sm:flex-row gap-4">
                                <Link
                                    className="inline-flex justify-center items-center px-6 py-3 rounded-lg font-medium text-blue-900 bg-white hover:bg-gray-50 transition-colors shadow-lg shadow-blue-900/20"
                                    href="#search"
                                >
                                    Find Organizations
                                </Link>
                                <Link
                                    className="inline-flex justify-center items-center px-6 py-3 rounded-lg font-medium border border-white/30 text-white hover:bg-blue-700/50 transition-colors"
                                    href="#register"
                                >
                                    Register Your NGO
                                    <ChevronRight className="ml-2 h-4 w-4" />
                                </Link>
                            </div>
                        </div>

                        {/* Hero image/illustration - Adding a distinctive visual element */}
                        <div className="hidden md:block relative">
                            <div className="absolute inset-0 bg-blue-600 rounded-2xl transform rotate-12 opacity-30" />
                            <div className="relative bg-blue-700 rounded-2xl p-6 shadow-xl">
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="flex flex-col space-y-4">
                                        <div className="bg-blue-600 h-24 rounded-lg p-4 flex items-center">
                                            <Users className="h-10 w-10 text-blue-300" />
                                            <div className="ml-3">
                                                <div className="text-sm text-blue-200">
                                                    Total NGOs
                                                </div>
                                                <div className="text-2xl font-bold text-white">
                                                    5,280+
                                                </div>
                                            </div>
                                        </div>
                                        <div className="bg-blue-600/60 h-40 rounded-lg p-4">
                                            <div className="w-full h-full bg-gradient-to-br from-blue-500/20 to-transparent rounded flex items-center justify-center">
                                                <div className="text-center">
                                                    <Globe className="h-8 w-8 text-blue-300 mx-auto mb-2" />
                                                    <div className="text-sm font-medium text-blue-100">
                                                        Global Reach
                                                    </div>
                                                    <div className="text-xs text-blue-200 mt-1">
                                                        120+ countries
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex flex-col space-y-4">
                                        <div className="bg-blue-600/60 h-40 rounded-lg p-4">
                                            <div className="w-full h-full bg-gradient-to-br from-blue-500/20 to-transparent rounded flex items-center justify-center">
                                                <div className="text-center">
                                                    <Target className="h-8 w-8 text-blue-300 mx-auto mb-2" />
                                                    <div className="text-sm font-medium text-blue-100">
                                                        Impact Focus
                                                    </div>
                                                    <div className="text-xs text-blue-200 mt-1">
                                                        25+ sectors
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="bg-blue-600 h-24 rounded-lg p-4 flex items-center">
                                            <FileText className="h-10 w-10 text-blue-300" />
                                            <div className="ml-3">
                                                <div className="text-sm text-blue-200">
                                                    Beneficiaries
                                                </div>
                                                <div className="text-2xl font-bold text-white">
                                                    10M+
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Unique divider */}
                <div className="absolute bottom-0 left-0 right-0">
                    <svg
                        className="w-full h-auto fill-white"
                        style={{ marginBottom: "-1px" }}
                        viewBox="0 0 1920 140"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path d="M0,0 C347,84 760.5,126 1241,126 C1626.5,126 1880,98 2000,0 L2000,140 L0,140 L0,0 Z" />
                    </svg>
                </div>
            </section>

            {/* Search Section - Professional refinement */}
            <section
                className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 -mt-20 mb-20 relative z-10"
                id="search"
            >
                <div className="bg-white border border-gray-100 rounded-2xl shadow-xl p-6 sm:p-8">
                    <div className="flex items-center mb-4">
                        <div className="h-10 w-1 bg-blue-600 rounded-full mr-3" />
                        <h2 className="text-xl font-semibold text-gray-900">
                            Find Organizations
                        </h2>
                    </div>

                    <div className="flex flex-col space-y-4">
                        <div className="flex flex-col md:flex-row gap-4">
                            <div className="relative flex-grow">
                                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                    <Search className="h-5 w-5 text-gray-400" />
                                </div>
                                <input
                                    className="block w-full pl-11 pr-4 py-3.5 rounded-xl border border-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 placeholder-gray-400 shadow-sm"
                                    placeholder="Search by organization name, registration number..."
                                    type="text"
                                    value={searchTerm}
                                    onChange={(e) =>
                                        setSearchTerm(e.target.value)
                                    }
                                />
                            </div>

                            <div className="relative">
                                <button
                                    className="w-full md:w-auto flex items-center justify-center gap-2 px-6 py-3.5 rounded-xl font-medium transition-all border border-gray-200 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-300 shadow-sm"
                                    onClick={() =>
                                        setIsFilterMenuOpen(!isFilterMenuOpen)
                                    }
                                >
                                    <Filter className="h-5 w-5" />
                                    <span>Filter</span>
                                    <ChevronDown className="h-4 w-4" />
                                </button>

                                {isFilterMenuOpen && (
                                    <div className="absolute right-0 mt-2 p-6 rounded-xl shadow-xl z-10 w-80 bg-white border border-gray-100">
                                        <div className="flex justify-between items-center mb-5">
                                            <h3 className="font-semibold text-gray-900">
                                                Filter Results
                                            </h3>
                                            <button
                                                className="p-1 rounded-full hover:bg-gray-100 text-gray-500"
                                                onClick={() =>
                                                    setIsFilterMenuOpen(false)
                                                }
                                            >
                                                <X size={16} />
                                            </button>
                                        </div>

                                        {/* Organization Type */}
                                        <div className="mb-6">
                                            <h4 className="text-sm font-medium mb-3 text-gray-700 flex items-center">
                                                <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-2" />
                                                Organization Type
                                            </h4>
                                            <div className="grid grid-cols-2 gap-2">
                                                {filterOptions
                                                    .slice(0, 4)
                                                    .map((option) => {
                                                        const isSelected =
                                                            selectedFilters.includes(
                                                                option.id,
                                                            );

                                                        return (
                                                            <div
                                                                key={option.id}
                                                                className={`flex items-center gap-2 px-3 py-2.5 rounded-lg cursor-pointer transition-colors
                          ${
                              isSelected
                                  ? "bg-blue-50 text-blue-700 border border-blue-200 shadow-sm"
                                                                : "bg-white text-gray-700 hover:bg-gray-50 border border-gray-200 hover:border-gray-300"
                          }`}
                                                                role="button"
                                                                tabIndex={0}
                                                                onClick={() =>
                                                                    handleFilterToggle(
                                                                        option.id,
                                                                    )
                                                                }
                                                                onKeyDown={() => ({})}
                                                            >
                                                                {isSelected && (
                                                                    <div className="absolute right-2 top-2">
                                                                        <Check
                                                                            className="text-blue-500"
                                                                            size={
                                                                                12
                                                                            }
                                                                        />
                                                                    </div>
                                                                )}
                                                                {option.icon}
                                                                <span className="text-sm">
                                                                    {
                                                                        option.label
                                                                    }
                                                                </span>
                                                            </div>
                                                        );
                                                    })}
                                            </div>
                                        </div>

                                        {/* Sectors */}
                                        <div className="mb-6">
                                            <h4 className="text-sm font-medium mb-3 text-gray-700 flex items-center">
                                                <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-2" />
                                                Sectors
                                            </h4>
                                            <div className="grid grid-cols-2 gap-2">
                                                {filterOptions
                                                    .slice(4, 7)
                                                    .map((option) => {
                                                        const isSelected =
                                                            selectedFilters.includes(
                                                                option.id,
                                                            );

                                                        return (
                                                            <div
                                                                key={option.id}
                                                                className={`flex items-center gap-2 px-3 py-2.5 rounded-lg cursor-pointer transition-colors
                          ${
                              isSelected
                                  ? "bg-blue-50 text-blue-700 border border-blue-200 shadow-sm"
                                                                : "bg-white text-gray-700 hover:bg-gray-50 border border-gray-200 hover:border-gray-300"
                          }`}
                                                                role="button"
                                                                tabIndex={0}
                                                                onClick={() =>
                                                                    handleFilterToggle(
                                                                        option.id,
                                                                    )
                                                                }
                                                                onKeyDown={() => ({})}
                                                            >
                                                                {isSelected && (
                                                                    <div className="absolute right-2 top-2">
                                                                        <Check
                                                                            className="text-blue-500"
                                                                            size={
                                                                                12
                                                                            }
                                                                        />
                                                                    </div>
                                                                )}
                                                                {option.icon}
                                                                <span className="text-sm">
                                                                    {
                                                                        option.label
                                                                    }
                                                                </span>
                                                            </div>
                                                        );
                                                    })}
                                            </div>
                                        </div>

                                        {/* Target Groups */}
                                        <div className="mb-6">
                                            <h4 className="text-sm font-medium mb-3 text-gray-700 flex items-center">
                                                <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-2" />
                                                Target Groups
                                            </h4>
                                            <div className="grid grid-cols-2 gap-2">
                                                {filterOptions
                                                    .slice(7)
                                                    .map((option) => {
                                                        const isSelected =
                                                            selectedFilters.includes(
                                                                option.id,
                                                            );

                                                        return (
                                                            <div
                                                                key={option.id}
                                                                className={`flex items-center gap-2 px-3 py-2.5 rounded-lg cursor-pointer transition-colors
                          ${
                              isSelected
                                                                ? "bg-blue-50 text-blue-700 border border-blue-200 shadow-sm"
                                                                : "bg-white text-gray-700 hover:bg-gray-50 border border-gray-200 hover:border-gray-300"
                          }`}
                                                                role="button"
                                                                tabIndex={0}
                                                                onClick={() =>
                                                                    handleFilterToggle(
                                                                        option.id,
                                                                    )
                                                                }
                                                                onKeyDown={() => ({})}
                                                            >
                                                                {isSelected && (
                                                                    <div className="absolute right-2 top-2">
                                                                        <Check
                                                                            className="text-blue-500"
                                                                            size={
                                                                                12
                                                                            }
                                                                        />
                                                                    </div>
                                                                )}
                                                                {option.icon}
                                                                <span className="text-sm">
                                                                    {
                                                                        option.label
                                                                    }
                                                                </span>
                                                            </div>
                                                        );
                                                    })}
                                            </div>
                                        </div>

                                        <div className="flex justify-between">
                                            <button
                                                className="text-sm text-blue-600 hover:text-blue-800"
                                                onClick={() =>
                                                    setSelectedFilters([])
                                                }
                                            >
                                                Clear all
                                            </button>
                                            <button
                                                className="px-4 py-2 text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-blue-700"
                                                onClick={() =>
                                                    setIsFilterMenuOpen(false)
                                                }
                                            >
                                                Apply Filters
                                            </button>
                                        </div>
                                    </div>
                                )}
                            </div>

                            <button className="px-6 py-3.5 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-xl transition-colors shadow-md">
                                Search
                            </button>
                        </div>

                        {/* Selected Filters - More professional styling */}
                        {selectedFilters.length > 0 && (
                            <div className="flex flex-wrap gap-2 mt-3 p-3 bg-gray-50 rounded-lg border border-gray-100">
                                {selectedFilters.map((filter) => {
                                    const filterOption = filterOptions.find(
                                        (opt) => opt.id === filter,
                                    );

                                    return (
                                        <div
                                            key={filter}
                                            className="flex items-center rounded-lg px-3 py-1.5 text-sm bg-white text-blue-700 border border-blue-100 shadow-sm"
                                        >
                                            {filterOption?.icon}
                                            <span className="ml-1.5 font-medium">
                                                {filterOption?.label}
                                            </span>
                                            <button
                                                className="ml-2 text-blue-500 hover:text-blue-700 p-0.5 hover:bg-blue-50 rounded-full"
                                                onClick={() =>
                                                    removeFilter(filter)
                                                }
                                            >
                                                <X size={14} />
                                            </button>
                                        </div>
                                    );
                                })}
                                <button
                                    className="text-sm text-blue-600 hover:text-blue-800 ml-2"
                                    onClick={() => setSelectedFilters([])}
                                >
                                    Clear all
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </section>

            {/* Featured Organizations - Professional and unique styling */}
            <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
                <div className="flex justify-between items-end mb-12">
                    <div>
                        <div className="flex items-center">
                            <div className="h-8 w-1 bg-blue-600 rounded-full mr-3" />
                            <h2 className="text-2xl font-bold text-gray-900">
                                Featured Organizations
                            </h2>
                        </div>
                        <p className="mt-2 text-gray-600 ml-4">
                            Discover prominent organizations making an impact
                            across various sectors
                        </p>
                    </div>
                    <Link
                        className="flex items-center font-medium text-blue-600 hover:text-blue-800 px-4 py-2 rounded-lg hover:bg-blue-50 transition-colors"
                        href="/organizations"
                    >
                        View all
                        <ArrowRight className="ml-1 h-4 w-4" />
                    </Link>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {/* Card 1 - Distinctive professional styling */}
                    <div className="group bg-white border border-gray-100 rounded-2xl overflow-hidden hover:shadow-xl transition-all duration-300 relative">
                        <div className="absolute top-0 left-0 w-full h-1 bg-blue-600" />
                        <div className="h-52 bg-gradient-to-r from-blue-600 to-blue-700 relative overflow-hidden">
                            <div className="absolute inset-0 bg-blue-900/30 opacity-0 group-hover:opacity-100 transition-opacity" />
                            <div className="absolute bottom-0 left-0 right-0 p-6">
                                <div className="transform translate-y-2 group-hover:translate-y-0 transition-transform">
                                    <h3 className="font-bold text-xl text-white">
                                        Global Health Initiative
                                    </h3>
                                    <p className="text-sm text-blue-100">
                                        Reg. No: NGO-2023-1254
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className="p-6">
                            <div className="flex justify-between items-center mb-4">
                                <span className="inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium bg-blue-50 text-blue-700 border border-blue-100">
                                    <Check className="mr-1" size={12} />
                                    Verified
                                </span>
                                <span className="text-sm text-gray-500 flex items-center">
                                    <Clock className="mr-1" size={14} />
                                    Est. 2018
                                </span>
                            </div>

                            <div className="flex flex-wrap gap-2 mb-4">
                                <span className="text-xs px-2.5 py-1 rounded-full bg-gray-100 text-gray-800">
                                    International
                                </span>
                                <span className="text-xs px-2.5 py-1 rounded-full bg-green-50 text-green-700">
                                    Health
                                </span>
                                <span className="text-xs px-2.5 py-1 rounded-full bg-yellow-50 text-yellow-700">
                                    Children
                                </span>
                            </div>
                            <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                                Working to improve healthcare access in
                                underserved communities with a focus on
                                preventative care and education.
                            </p>
                            <div className="flex justify-between items-center pt-2 border-t border-gray-100">
                                <Link
                                    className="inline-flex items-center font-medium text-sm text-blue-600 hover:text-blue-800 group-hover:underline"
                                    href="/organizations/ngo-2023-1254"
                                >
                                    View details
                                    <ArrowRight className="ml-1 h-4 w-4 transform group-hover:translate-x-1 transition-transform" />
                                </Link>
                                <Link
                                    className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 hover:bg-gray-50 px-2 py-1 rounded"
                                    href="https://globalhealthinitiative.org"
                                >
                                    Website
                                    <ExternalLink className="ml-1 h-3 w-3" />
                                </Link>
                            </div>
                        </div>
                    </div>

                    {/* Card 2 - Distinctive professional styling */}
                    <div className="group bg-white border border-gray-100 rounded-2xl overflow-hidden hover:shadow-xl transition-all duration-300 relative">
                        <div className="absolute top-0 left-0 w-full h-1 bg-green-600" />
                        <div className="h-52 bg-gradient-to-r from-green-600 to-green-700 relative overflow-hidden">
                            <div className="absolute inset-0 bg-green-900/30 opacity-0 group-hover:opacity-100 transition-opacity" />
                            <div className="absolute bottom-0 left-0 right-0 p-6">
                                <div className="transform translate-y-2 group-hover:translate-y-0 transition-transform">
                                    <h3 className="font-bold text-xl text-white">
                                        Education For All
                                    </h3>
                                    <p className="text-sm text-green-100">
                                        Reg. No: NGO-2022-0876
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className="p-6">
                            <div className="flex justify-between items-center mb-4">
                                <span className="inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium bg-green-50 text-green-700 border border-green-100">
                                    <Check className="mr-1" size={12} />
                                    Verified
                                </span>
                                <span className="text-sm text-gray-500 flex items-center">
                                    <Clock className="mr-1" size={14} />
                                    Est. 2020
                                </span>
                            </div>

                            <div className="flex flex-wrap gap-2 mb-4">
                                <span className="text-xs px-2.5 py-1 rounded-full bg-gray-100 text-gray-800">
                                    NGO
                                </span>
                                <span className="text-xs px-2.5 py-1 rounded-full bg-green-50 text-green-700">
                                    Education
                                </span>
                                <span className="text-xs px-2.5 py-1 rounded-full bg-yellow-50 text-yellow-700">
                                    Children
                                </span>
                            </div>
                            <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                                Dedicated to ensuring quality education for
                                underprivileged children through innovative
                                teaching methods and resources.
                            </p>
                            <div className="flex justify-between items-center pt-2 border-t border-gray-100">
                                <Link
                                    className="inline-flex items-center font-medium text-sm text-blue-600 hover:text-blue-800 group-hover:underline"
                                    href="/organizations/ngo-2022-0876"
                                >
                                    View details
                                    <ArrowRight className="ml-1 h-4 w-4 transform group-hover:translate-x-1 transition-transform" />
                                </Link>
                                <Link
                                    className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 hover:bg-gray-50 px-2 py-1 rounded"
                                    href="https://educationforall.org"
                                >
                                    Website
                                    <ExternalLink className="ml-1 h-3 w-3" />
                                </Link>
                            </div>
                        </div>
                    </div>

                    {/* Card 3 - Distinctive professional styling */}
                    <div className="group bg-white border border-gray-100 rounded-2xl overflow-hidden hover:shadow-xl transition-all duration-300 relative">
                        <div className="absolute top-0 left-0 w-full h-1 bg-purple-600" />
                        <div className="h-52 bg-gradient-to-r from-purple-600 to-purple-700 relative overflow-hidden">
                            <div className="absolute inset-0 bg-purple-900/30 opacity-0 group-hover:opacity-100 transition-opacity" />
                            <div className="absolute bottom-0 left-0 right-0 p-6">
                                <div className="transform translate-y-2 group-hover:translate-y-0 transition-transform">
                                    <h3 className="font-bold text-xl text-white">
                                        Women Empowerment Network
                                    </h3>
                                    <p className="text-sm text-purple-100">
                                        Reg. No: CBO-2021-5432
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className="p-6">
                            <div className="flex justify-between items-center mb-4">
                                <span className="inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium bg-purple-50 text-purple-700 border border-purple-100">
                                    <Check className="mr-1" size={12} />
                                    Verified
                                </span>
                                <span className="text-sm text-gray-500 flex items-center">
                                    <Clock className="mr-1" size={14} />
                                    Est. 2021
                                </span>
                            </div>

                            <div className="flex flex-wrap gap-2 mb-4">
                                <span className="text-xs px-2.5 py-1 rounded-full bg-gray-100 text-gray-800">
                                    CBO
                                </span>
                                <span className="text-xs px-2.5 py-1 rounded-full bg-green-50 text-green-700">
                                    Education
                                </span>
                                <span className="text-xs px-2.5 py-1 rounded-full bg-pink-50 text-pink-700">
                                    Women
                                </span>
                            </div>
                            <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                                Supporting women through skills development,
                                entrepreneurship training, and advocacy for
                                gender equality.
                            </p>
                            <div className="flex justify-between items-center pt-2 border-t border-gray-100">
                                <Link
                                    className="inline-flex items-center font-medium text-sm text-blue-600 hover:text-blue-800 group-hover:underline"
                                    href="/organizations/cbo-2021-5432"
                                >
                                    View details
                                    <ArrowRight className="ml-1 h-4 w-4 transform group-hover:translate-x-1 transition-transform" />
                                </Link>
                                <Link
                                    className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 hover:bg-gray-50 px-2 py-1 rounded"
                                    href="https://womenempowermentnetwork.org"
                                >
                                    Website
                                    <ExternalLink className="ml-1 h-3 w-3" />
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Stats Section - Professional and distinctive */}
            <section className="py-20 relative">
                <div className="absolute inset-0 bg-gray-50" />
                <div className="absolute left-0 right-0 h-20 bg-gradient-to-b from-white to-gray-50 top-0" />
                <div className="absolute left-0 right-0 h-20 bg-gradient-to-t from-white to-gray-50 bottom-0" />

                <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-50 text-blue-700 mb-4">
                            <span className="w-2 h-2 bg-blue-500 rounded-full mr-2" />
                            Our Global Impact
                        </span>
                        <h2 className="text-3xl font-bold text-gray-900 mb-4">
                            Making a Difference in Numbers
                        </h2>
                        <p className="max-w-2xl mx-auto text-gray-600">
                            Tracking the collective impact of organizations
                            registered within our network
                        </p>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-10">
                        <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform hover:-translate-y-1 transition-all duration-300">
                            <div className="h-2 bg-blue-600" />
                            <div className="p-6">
                                <div className="w-12 h-12 bg-blue-50 rounded-full flex items-center justify-center mb-4">
                                    <Users className="h-6 w-6 text-blue-600" />
                                </div>
                                <p className="text-4xl font-bold text-gray-900 flex items-baseline">
                                    5,280
                                    <span className="text-xl text-blue-600 ml-1">
                                        +
                                    </span>
                                </p>
                                <p className="mt-2 text-gray-600">
                                    Registered Organizations
                                </p>
                            </div>
                        </div>
                        <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform hover:-translate-y-1 transition-all duration-300">
                            <div className="h-2 bg-green-600" />
                            <div className="p-6">
                                <div className="w-12 h-12 bg-green-50 rounded-full flex items-center justify-center mb-4">
                                    <Globe className="h-6 w-6 text-green-600" />
                                </div>
                                <p className="text-4xl font-bold text-gray-900 flex items-baseline">
                                    120
                                    <span className="text-xl text-green-600 ml-1">
                                        +
                                    </span>
                                </p>
                                <p className="mt-2 text-gray-600">
                                    Countries Reached
                                </p>
                            </div>
                        </div>
                        <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform hover:-translate-y-1 transition-all duration-300">
                            <div className="h-2 bg-purple-600" />
                            <div className="p-6">
                                <div className="w-12 h-12 bg-purple-50 rounded-full flex items-center justify-center mb-4">
                                    <FileText className="h-6 w-6 text-purple-600" />
                                </div>
                                <p className="text-4xl font-bold text-gray-900 flex items-baseline">
                                    25
                                    <span className="text-xl text-purple-600 ml-1">
                                        +
                                    </span>
                                </p>
                                <p className="mt-2 text-gray-600">
                                    Sectors Covered
                                </p>
                            </div>
                        </div>
                        <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform hover:-translate-y-1 transition-all duration-300">
                            <div className="h-2 bg-yellow-600" />
                            <div className="p-6">
                                <div className="w-12 h-12 bg-yellow-50 rounded-full flex items-center justify-center mb-4">
                                    <Target className="h-6 w-6 text-yellow-600" />
                                </div>
                                <p className="text-4xl font-bold text-gray-900 flex items-baseline">
                                    10M
                                    <span className="text-xl text-yellow-600 ml-1">
                                        +
                                    </span>
                                </p>
                                <p className="mt-2 text-gray-600">
                                    Beneficiaries
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* CTA Section - Professional and unique */}
            <section className="py-20 bg-white relative" id="register">
                <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative">
                    <div className="bg-gradient-to-br from-blue-600 to-blue-800 rounded-3xl overflow-hidden shadow-xl">
                        <div className="absolute top-0 right-0 w-80 h-80 bg-blue-500 rounded-full opacity-10 transform translate-x-1/2 -translate-y-1/2" />
                        <div className="absolute bottom-0 left-0 w-80 h-80 bg-blue-500 rounded-full opacity-10 transform -translate-x-1/2 translate-y-1/2" />

                        <div className="relative px-8 py-12 md:p-16 text-center md:text-left md:flex items-center">
                            <div className="md:w-3/5 mb-8 md:mb-0 md:pr-10">
                                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white/20 text-white mb-6">
                                    <span className="w-2 h-2 bg-white rounded-full mr-2" />
                                    Join Our Network
                                </span>
                                <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
                                    Ready to Register Your Organization?
                                </h2>
                                <p className="text-lg mb-8 text-blue-100 max-w-xl">
                                    Join our database and increase your
                                    organization&apos;s visibility and
                                    collaboration opportunities. Our platform
                                    connects NGOs with donors, volunteers, and
                                    partners.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
                                    <Link
                                        className="inline-flex justify-center items-center px-6 py-3 rounded-xl font-medium text-blue-900 bg-white hover:bg-blue-50 transition-colors shadow-lg shadow-blue-900/20"
                                        href="/auth/register"
                                    >
                                        Register Now
                                    </Link>
                                    <Link
                                        className="inline-flex justify-center items-center px-6 py-3 rounded-xl font-medium border border-white/30 text-white hover:bg-blue-700/50 transition-colors"
                                        href="/about"
                                    >
                                        Learn More
                                        <ChevronRight className="ml-2 h-4 w-4" />
                                    </Link>
                                </div>
                            </div>
                            <div className="md:w-2/5 hidden md:block">
                                <div className="relative">
                                    <div className="absolute inset-0 border-2 border-white/20 rounded-xl transform rotate-6" />
                                    <div className="relative bg-blue-700/80 backdrop-blur rounded-xl p-6 border border-white/10">
                                        <div className="flex items-center mb-4">
                                            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                                                <Check className="h-5 w-5 text-white" />
                                            </div>
                                            <div className="ml-3">
                                                <h3 className="text-xl font-bold text-white">
                                                    Registration Benefits
                                                </h3>
                                            </div>
                                        </div>
                                        <ul className="space-y-3">
                                            <li className="flex items-center text-blue-100">
                                                <div className="w-5 h-5 bg-blue-600/50 rounded-full flex items-center justify-center mr-2">
                                                    <Check className="h-3 w-3 text-white" />
                                                </div>
                                                <span className="text-sm">
                                                    Enhanced visibility to
                                                    donors
                                                </span>
                                            </li>
                                            <li className="flex items-center text-blue-100">
                                                <div className="w-5 h-5 bg-blue-600/50 rounded-full flex items-center justify-center mr-2">
                                                    <Check className="h-3 w-3 text-white" />
                                                </div>
                                                <span className="text-sm">
                                                    Networking opportunities
                                                </span>
                                            </li>
                                            <li className="flex items-center text-blue-100">
                                                <div className="w-5 h-5 bg-blue-600/50 rounded-full flex items-center justify-center mr-2">
                                                    <Check className="h-3 w-3 text-white" />
                                                </div>
                                                <span className="text-sm">
                                                    Access to funding
                                                    opportunities
                                                </span>
                                            </li>
                                            <li className="flex items-center text-blue-100">
                                                <div className="w-5 h-5 bg-blue-600/50 rounded-full flex items-center justify-center mr-2">
                                                    <Check className="h-3 w-3 text-white" />
                                                </div>
                                                <span className="text-sm">
                                                    Official verification status
                                                </span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Footer - Professional and refined */}
            <footer className="bg-gray-900 text-gray-400 pt-16 pb-8 relative">
                <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 via-purple-500 to-blue-400" />
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="grid grid-cols-1 md:grid-cols-6 gap-12 mb-12">
                        <div className="md:col-span-2">
                            <div className="flex items-center mb-6">
                                <div className="relative">
                                    <div className="h-10 w-10 bg-blue-600 rounded-lg flex items-center justify-center transform rotate-45 shadow-md">
                                        <span className="text-white font-bold text-lg transform -rotate-45">
                                            N
                                        </span>
                                    </div>
                                    <div className="absolute -bottom-1 -right-1 h-4 w-4 bg-blue-500 rounded-full border-2 border-gray-900" />
                                </div>
                                <div className="ml-3">
                                    <div className="text-lg font-bold text-white">
                                        <span className="font-light">my</span>
                                        NGO
                                    </div>
                                    <span className="text-xs text-gray-500 tracking-wider uppercase">
                                        Regulatory Authority
                                    </span>
                                </div>
                            </div>
                            <p className="text-sm mb-6 ml-1">
                                The Non-Governmental Regulatory Authority
                                oversees and supports organizations working for
                                social impact across multiple sectors.
                            </p>

                            <div className="flex space-x-4 ml-1">
                                <Link
                                    aria-label="Twitter"
                                    className="w-8 h-8 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-blue-600 hover:text-white transition-colors"
                                    href="/"
                                >
                                    <svg
                                        className="h-4 w-4"
                                        fill="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                                    </svg>
                                </Link>
                                <Link
                                    aria-label="Facebook"
                                    className="w-8 h-8 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-blue-600 hover:text-white transition-colors"
                                    href="/"
                                >
                                    <svg
                                        className="h-4 w-4"
                                        fill="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            clipRule="evenodd"
                                            d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                                            fillRule="evenodd"
                                        />
                                    </svg>
                                </Link>
                                <Link
                                    aria-label="LinkedIn"
                                    className="w-8 h-8 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-blue-600 hover:text-white transition-colors"
                                    href="/"
                                >
                                    <svg
                                        className="h-4 w-4"
                                        fill="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                                    </svg>
                                </Link>
                            </div>
                        </div>
                        <div>
                            <h3 className="text-white font-bold text-sm uppercase tracking-wider mb-4 relative inline-block">
                                <span className="relative z-10">
                                    Quick Links
                                </span>
                                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600" />
                            </h3>
                            <ul className="space-y-2">
                                <li>
                                    <Link
                                        className="hover:text-white transition-colors"
                                        href="/"
                                    >
                                        Home
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        className="hover:text-white transition-colors"
                                        href="/about"
                                    >
                                        About Us
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        className="hover:text-white transition-colors"
                                        href="/organizations"
                                    >
                                        Organizations
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        className="hover:text-white transition-colors"
                                        href="/resources"
                                    >
                                        Resources
                                    </Link>
                                </li>
                            </ul>
                        </div>
                        <div>
                            <h3 className="text-white font-bold text-sm uppercase tracking-wider mb-4 relative inline-block">
                                <span className="relative z-10">Resources</span>
                                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600" />
                            </h3>
                            <ul className="space-y-2">
                                <li>
                                    <Link
                                        className="hover:text-white transition-colors"
                                        href="/faq"
                                    >
                                        Guidelines
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        className="hover:text-white transition-colors"
                                        href="/faq"
                                    >
                                        Registration Process
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        className="hover:text-white transition-colors"
                                        href="/reports"
                                    >
                                        Annual Reports
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        className="hover:text-white transition-colors"
                                        href="/faq"
                                    >
                                        FAQ
                                    </Link>
                                </li>
                            </ul>
                        </div>
                        <div className="md:col-span-2">
                            <h3 className="text-white font-bold text-sm uppercase tracking-wider mb-4 relative inline-block">
                                <span className="relative z-10">
                                    Contact Us
                                </span>
                                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600" />
                            </h3>
                            <ul className="space-y-3">
                                <li className="flex items-center">
                                    <MapPin className="h-4 w-4 mr-2 text-blue-400" />
                                    <span>Area 49, Lilongwe</span>
                                </li>
                                <li className="flex items-center">
                                    <Mail className="h-4 w-4 mr-2 text-blue-400" />
                                    <Link
                                        className="hover:text-white transition-colors"
                                        href="mailto:<EMAIL>"
                                    >
                                        <EMAIL>
                                    </Link>
                                </li>
                                <li className="flex items-center">
                                    <Phone className="h-4 w-4 mr-2 text-blue-400" />
                                    <Link
                                        className="hover:text-white transition-colors"
                                        href="tel:+15551234567"
                                    >
                                        +265 (*************
                                    </Link>
                                </li>
                            </ul>
                            <div className="mt-6 bg-gray-800 rounded-xl p-4 border border-gray-700">
                                <h4 className="text-white font-medium mb-3 text-sm">
                                    Subscribe to our newsletter
                                </h4>
                                <div className="flex">
                                    <input
                                        className="w-full px-3 py-2 text-gray-900 bg-white rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        placeholder="Your email"
                                        type="email"
                                    />
                                    <button className="px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-r-lg transition-colors">
                                        Subscribe
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="border-t border-gray-800 mt-10 pt-8 flex flex-col md:flex-row justify-between items-center">
                        <p className="text-sm">
                            &copy; 2025 NGORA. All rights reserved.
                        </p>
                        <div className="mt-4 md:mt-0 flex space-x-6">
                            <Link
                                className="text-xs hover:text-white transition-colors"
                                href="/legal/privacy-policy"
                            >
                                Privacy Policy
                            </Link>
                            <Link
                                className="text-xs hover:text-white transition-colors"
                                href="/legal/terms-of-service"
                            >
                                Terms of Service
                            </Link>
                            <Link
                                className="text-xs hover:text-white transition-colors"
                                href="/legal/cookie-policy"
                            >
                                Cookie Policy
                            </Link>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    );
}
