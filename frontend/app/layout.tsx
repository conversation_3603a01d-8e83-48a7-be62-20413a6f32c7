import "@/styles/globals.css";
import clsx from "clsx";
import { Metadata, Viewport } from "next";
import * as React from "react";

import { ThemeProvider } from "./providers";
import { ThemeClient } from "./theme-client";

import { Toaster } from "@/components/ui/sonner";
import { siteConfig } from "@/config/site";

export const metadata: Metadata = {
    title: {
        default: siteConfig.name,
        template: `%s - ${siteConfig.name}`,
    },
    description: siteConfig.description,
    icons: {
        icon: "/favicon.ico",
    },
};

export const viewport: Viewport = {
    themeColor: [
        { media: "(prefers-color-scheme: light)", color: "white" },
        { media: "(prefers-color-scheme: dark)", color: "black" },
    ],
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
    return (
        <html suppressHydrationWarning lang="en">
            <head>
                <script
                    dangerouslySetInnerHTML={{
                        __html: `
              (() => {
                try {
                  const mode = localStorage.getItem("appearance") || "system";
                  const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
                  const isDark = mode === "dark" || (mode === "system" && prefersDark);
                  if (isDark) {
                    document.documentElement.classList.add("dark");
                  }
                } catch (_) {}
              })();
            `,
                    }}
                />
            </head>
            <body className={clsx("min-h-screen antialiased")}>
                <ThemeProvider disableTransitionOnChange enableSystem attribute="class" defaultTheme="system">
                    <ThemeClient />
                    {children}
                    <Toaster richColors position="bottom-right" />
                </ThemeProvider>
            </body>
        </html>
    );
}
