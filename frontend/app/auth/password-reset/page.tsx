"use client";
import { ArrowLeft, Mail, Send } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";

import { AuthFormFooter } from "@/components/auth/auth-form-sections";
import { OneTimePassword } from "@/components/inputs/one-time-password";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import * as AuthService from "@/services/AuthService";
import * as SessionService from "@/services/SessionService";

type Step = "email" | "otp";

const TwoFAVerificationPage = () => {
    const [currentStep, setCurrentStep] = useState<Step>("email");
    const [email, setEmail] = useState("");
    const [otp, setOtp] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [resendLoading, setResendLoading] = useState(false);
    const [timeLeft, setTimeLeft] = useState(60);
    const [canResend, setCanResend] = useState(false);

    const router = useRouter();

    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const emailParam = urlParams.get("email");

        if (emailParam) {
            setEmail(emailParam);
            setCurrentStep("otp");
        }
    }, []);

    useEffect(() => {
        if (currentStep === "otp" && timeLeft > 0) {
            const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);

            return () => clearTimeout(timer);
        } else if (timeLeft === 0) {
            setCanResend(true);
        }
    }, [currentStep, timeLeft]);

    const validateEmail = (email: string) => {
        return /\S+@\S+\.\S+/.test(email);
    };

    const handleSendCode = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!email) {
            toast.error("Please enter your email address");

            return;
        }

        if (!validateEmail(email)) {
            toast.error("Please enter a valid email address");

            return;
        }

        setIsLoading(true);

        try {
            const response = await AuthService.resendVerificationCode(email);

            if (!response.success) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return;
            }

            toast.success("Verification code sent to your email!");
            setCurrentStep("otp");
            setTimeLeft(60);
            setCanResend(false);
        } catch (error) {
            toast.error("Failed to send verification code. Please try again.");
        } finally {
            setIsLoading(false);
        }
    };

    const handleVerifyOTP = async (e: React.FormEvent) => {
        e.preventDefault();

        if (otp.length !== 6) {
            toast.error("Please enter the complete 6-digit code");

            return;
        }

        setIsLoading(true);

        try {
            const response = await AuthService.verifyTwoFA({
                email,
                code: otp,
            });

            if (!response.success) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }
                setOtp("");

                return;
            }

            if (response.success && response.data) {
                toast.success("Account verified successfully!");
                SessionService.startSession(response.data, "start", router);
            }
        } catch (error) {
            toast.error("Verification failed. Please try again.");
            setOtp("");
        } finally {
            setIsLoading(false);
        }
    };

    const handleResendCode = async () => {
        setResendLoading(true);

        try {
            const response = await AuthService.resendVerificationCode(email);

            if (response.success) {
                toast.success("Verification code sent!");
                setTimeLeft(60);
                setCanResend(false);
                setOtp(""); // Clear current OTP
            } else {
                for (const error of response.errors) {
                    toast.error(error.message);
                }
            }
        } catch (error) {
            toast.error("Failed to resend code. Please try again.");
        } finally {
            setResendLoading(false);
        }
    };

    const handleBackToEmail = () => {
        setCurrentStep("email");
        setOtp("");
        setTimeLeft(60);
        setCanResend(false);
    };

    const maskEmail = (email: string) => {
        if (!email) return "";
        const [localPart, domain] = email.split("@");

        if (localPart.length <= 2) return email;

        const maskedLocal =
            localPart.charAt(0) +
            "*".repeat(localPart.length - 2) +
            localPart.charAt(localPart.length - 1);

        return `${maskedLocal}@${domain}`;
    };

    return (
        <div className="flex min-h-screen">
            <div className="w-full flex items-center justify-center p-6 sm:p-12">
                <div className="w-full max-w-md">
                    {currentStep === "email" ? (
                        // Email Input Step
                        <>
                            <div className="text-center mb-8">
                                <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                                    <Send className="w-8 h-8 text-primary" />
                                </div>

                                <h2 className="text-2xl font-semibold mb-2">
                                    Email Verification
                                </h2>

                                <p className="text-muted-foreground">
                                    Enter your email address to receive a
                                    verification code
                                </p>
                            </div>

                            <form
                                className="space-y-6"
                                onSubmit={handleSendCode}
                            >
                                <Input
                                    required
                                    className="mt-1"
                                    id="email"
                                    label="Email Address"
                                    placeholder="<EMAIL>"
                                    type="email"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                />

                                <Button
                                    className="w-full"
                                    loading={isLoading}
                                    loadingText="Sending Code..."
                                    type="submit"
                                >
                                    Send Verification Code
                                </Button>

                                <div className="text-center">
                                    <Button
                                        className="text-muted-foreground hover:text-foreground"
                                        type="button"
                                        variant="ghost"
                                        onClick={() =>
                                            router.push("/auth/register")
                                        }
                                    >
                                        <ArrowLeft className="w-4 h-4 mr-2" />
                                        Back to Registration
                                    </Button>
                                </div>
                            </form>
                        </>
                    ) : (
                        <>
                            <div className="text-center mb-8">
                                <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                                    <Mail className="w-8 h-8 text-primary" />
                                </div>

                                <h2 className="text-2xl font-semibold mb-2">
                                    Check Your Email
                                </h2>

                                <p className="text-muted-foreground mb-2">
                                    We&apos;ve sent a 6-digit verification code
                                    to
                                </p>

                                <p className="font-medium text-primary mb-4">
                                    {maskEmail(email)}
                                </p>

                                <p className="text-sm text-muted-foreground">
                                    Enter the code below to verify your account
                                </p>
                            </div>

                            <form
                                className="space-y-6"
                                onSubmit={handleVerifyOTP}
                            >
                                <div className="flex flex-col items-center space-y-4">
                                    <Label className="text-sm font-medium">
                                        Verification Code
                                    </Label>

                                    <OneTimePassword
                                        length={6}
                                        value={otp}
                                        onChange={setOtp}
                                    />

                                    <p className="text-xs text-muted-foreground text-center">
                                        {otp.length === 0 &&
                                            "Please enter your 6-digit code"}
                                        {otp.length > 0 &&
                                            otp.length < 6 &&
                                            `${6 - otp.length} digits remaining`}
                                        {otp.length === 6 && "Code complete"}
                                    </p>
                                </div>

                                <Button
                                    className="w-full"
                                    disabled={otp.length !== 6}
                                    loading={isLoading}
                                    loadingText="Verifying..."
                                    type="submit"
                                >
                                    Verify Account
                                </Button>
                            </form>

                            <div className="mt-6 space-y-4">
                                <div className="text-center">
                                    <p className="text-sm text-muted-foreground mb-3">
                                        Didn&apos;t receive the code?
                                    </p>

                                    {canResend ? (
                                        <Button
                                            className="w-full"
                                            loading={resendLoading}
                                            loadingText="Sending..."
                                            type="button"
                                            variant="outline"
                                            onClick={handleResendCode}
                                        >
                                            Resend Verification Code
                                        </Button>
                                    ) : (
                                        <div className="text-sm text-muted-foreground">
                                            <p>You can request a new code in</p>
                                            <p className="font-medium text-primary">
                                                {Math.floor(timeLeft / 60)}:
                                                {(timeLeft % 60)
                                                    .toString()
                                                    .padStart(2, "0")}
                                            </p>
                                        </div>
                                    )}
                                </div>

                                <div className="flex items-center justify-center space-x-4 pt-4 border-t">
                                    <Button
                                        className="text-muted-foreground hover:text-foreground"
                                        type="button"
                                        variant="ghost"
                                        onClick={handleBackToEmail}
                                    >
                                        <ArrowLeft className="w-4 h-4 mr-2" />
                                        Change Email
                                    </Button>
                                </div>
                            </div>
                        </>
                    )}

                    <AuthFormFooter page="2fa" />
                </div>
            </div>
        </div>
    );
};

export default TwoFAVerificationPage;
