"use client";

import { usePathname, useRouter } from "next/navigation";
import { ReactNode, useEffect, useMemo } from "react";

import { useAuth } from "@/composables/useStore";

interface Props {
    node: ReactNode;
}

const ProtectedRoute = ({ node }: Props): ReactNode => {
    const { isAuthenticated, session, isLoading } = useAuth();
    const router = useRouter();
    const currentPath = usePathname();

    const pathInfo = useMemo(
        () => ({
            isLogin: currentPath === "/auth/login",
            isRegister: currentPath === "/auth/register",
            is2FA: currentPath === "/auth/2fa",
            isAuth: currentPath.startsWith("/auth"),
        }),
        [currentPath],
    );

    useEffect(() => {
        if (isLoading) return;

        const authType = session?.auth_type;

        if (isAuthenticated) {
            if (authType === "MAIN") {
                if (pathInfo.isAuth) {
                    router.replace("/dashboard");

                    return;
                }
            } else if (authType === "LOGIN_2FA" || authType === "VERIFICATION") {
                if (!pathInfo.is2FA) {
                    router.replace("/auth/2fa");

                    return;
                }
            }
        } else {
            if (pathInfo.is2FA) {
                router.replace("/auth/login");

                return;
            }

            if (!pathInfo.isLogin && !pathInfo.isRegister) {
                const redirect = pathInfo.isAuth ? "/auth/login" : "/auth/login?redirect=unauthenticated";

                router.replace(redirect);

                return;
            }
        }
    }, [isLoading, isAuthenticated, session?.auth_type, pathInfo, router]);

    if (isLoading) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-background">
                <div className="flex flex-col items-center space-y-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
                </div>
            </div>
        );
    }

    return node;
};

export default ProtectedRoute;
